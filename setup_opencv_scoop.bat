@echo off
echo Setting up OpenCV environment for scoop installation...

REM 设置OpenCV路径变量
set OPENCV_ROOT=C:\App\scoop\apps\opencv\current
set OPENCV_BUILD=%OPENCV_ROOT%\build
set OPENCV_BIN=%OPENCV_ROOT%\x64\vc16\bin

echo Checking OpenCV installation...

REM 检查OpenCV是否存在
if not exist "%OPENCV_ROOT%" (
    echo Error: OpenCV not found at %OPENCV_ROOT%
    echo Please make sure OpenCV is installed via scoop
    echo Run: scoop install opencv
    pause
    exit /b 1
)

if not exist "%OPENCV_BUILD%" (
    echo Error: OpenCV build directory not found at %OPENCV_BUILD%
    echo Your OpenCV installation might be incomplete
    pause
    exit /b 1
)

if not exist "%OPENCV_BIN%" (
    echo Error: OpenCV bin directory not found at %OPENCV_BIN%
    echo Your OpenCV installation might be incomplete
    pause
    exit /b 1
)

echo OpenCV found at: %OPENCV_ROOT%
echo Build directory: %OPENCV_BUILD%
echo Bin directory: %OPENCV_BIN%

REM 设置环境变量
echo Setting environment variables...
set OpenCV_DIR=%OPENCV_BUILD%
set PATH=%OPENCV_BIN%;%PATH%

echo Environment variables set:
echo   OpenCV_DIR = %OpenCV_DIR%
echo   PATH includes: %OPENCV_BIN%

REM 验证OpenCV DLL
echo.
echo Checking OpenCV DLLs...
if exist "%OPENCV_BIN%\opencv_world*.dll" (
    echo ✓ OpenCV world DLL found
) else (
    echo ⚠ OpenCV world DLL not found, checking individual DLLs...
    if exist "%OPENCV_BIN%\opencv_core*.dll" (
        echo ✓ OpenCV core DLL found
    ) else (
        echo ✗ OpenCV DLLs not found in %OPENCV_BIN%
    )
)

echo.
echo OpenCV environment setup complete!
echo You can now run build.bat to compile the project.
echo.

REM 询问是否立即构建
set /p build_now="Do you want to build the project now? (Y/N): "
if /i "%build_now%"=="Y" (
    echo.
    echo Building project...
    call build.bat
) else (
    echo.
    echo To build later, run: build.bat
    echo Make sure to run this script first to set up the environment.
)

pause
