# FPGA缓冲区到图像转换工具

## 项目概述

这个项目提供了一个命令行工具，可以将FPGA缓冲区数据文件转换为可视化图像。每行数据对应一帧，程序会解析其中的blob（斑点）信息并生成相应的图像文件。

## 主要特性

- **双版本支持**：
  - 简化版本：无依赖，生成SVG格式图像
  - 完整版本：需要OpenCV，生成PNG格式图像

- **自动化处理**：
  - 批量处理多帧数据
  - 自动创建输出文件夹
  - 生成HTML报告（简化版本）

- **可视化功能**：
  - 显示blob的位置和大小
  - 内外圆圈标示
  - 数字标签和帧信息

## 文件结构

```
fpgabuf_to_img/
├── main.cpp                 # 完整版本主程序（需要OpenCV）
├── simple_converter.cpp     # 简化版本主程序（无依赖）
├── fpgaUtil/                # FPGA工具库
│   ├── fpga_util.h
│   ├── fpga_util.cpp
│   ├── api_struct_define.h
│   └── cv_define.h
├── demo/                    # 示例数据文件
│   ├── FpgaBuf_0.txt
│   ├── FpgaBuf_1.txt
│   └── FpgaBuf_2.txt
├── build_simple.bat        # 简化版本构建脚本
├── build.bat               # 完整版本构建脚本
├── test_simple.bat         # 简化版本测试脚本
├── test.bat                # 完整版本测试脚本
├── CMakeLists.txt          # CMake构建配置
├── README.md               # 详细说明文档
├── QUICKSTART.md           # 快速开始指南
└── 项目说明.md             # 本文件
```

## 快速开始

### 1. 构建程序（推荐简化版本）
```bash
双击运行 build_simple.bat
```

### 2. 测试程序
```bash
双击运行 test_simple.bat
```

### 3. 使用程序
```bash
simple_converter.exe demo/FpgaBuf_0.txt output_folder
```

## 输出说明

### 简化版本
- 生成SVG矢量图像文件
- 生成HTML汇总报告
- 可在浏览器中查看

### 完整版本
- 生成PNG位图图像文件
- 需要安装OpenCV

## 数据格式

输入文件为文本格式，每行包含一帧的FPGA缓冲区数据，数据以空格分隔的数字形式存储。

## 依赖项

### 简化版本
- C++17编译器（如MinGW-w64或Visual Studio）
- 无其他外部依赖

### 完整版本
- C++17编译器
- OpenCV 4.x
- Eigen3
- CMake 3.10+

## 构建选项

1. **最简单**：`build_simple.bat` - 生成SVG图像，无依赖
2. **功能完整**：`build.bat` - 生成PNG图像，需要OpenCV
3. **手动构建**：使用CMake或直接编译

## 使用场景

- FPGA数据可视化
- 实时图像处理结果分析
- 算法调试和验证
- 数据质量检查

## 技术特点

- 高效的数据解析
- 灵活的输出格式
- 跨平台兼容性
- 易于集成和扩展

## 支持的平台

- Windows（主要测试平台）
- Linux（理论支持）
- macOS（理论支持）

## 许可证

基于现有FPGA工具库构建，请遵循相应的许可证要求。

---

**建议使用简化版本开始，它无需安装任何额外依赖，可以快速上手使用。**
