#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <filesystem>
#include <opencv2/core/core.hpp>
#include <opencv2/highgui/highgui.hpp>
#include <opencv2/imgproc/imgproc.hpp>
#include "fpgaUtil/fpga_util.h"

using namespace std;
using namespace cv;
namespace fs = std::filesystem;

// 将一行文本数据转换为字节缓冲区
int line2buffer(const string& line, char* buffer) {
    uint8_t ch = 0;
    int count = 0;

    for (size_t i = 0; i < line.size(); i++) {
        char c = line[i];
        if (c == ' ') {
            buffer[count] = (char)ch;
            count++;
            ch = 0;
        } else if (c >= '0' && c <= '9') {
            ch = ch * 10 + c - '0';
        }
    }

    // 处理最后一个数字（如果行末没有空格）
    if (ch != 0) {
        buffer[count] = (char)ch;
        count++;
    }

    return count;
}

// 绘制blob图像
Mat DrawBlobs(const fpga_util::FramePointsXYR& frame_points_xyr, int frame_index) {
    const vector<double>& xs = frame_points_xyr.xs;
    const vector<double>& ys = frame_points_xyr.ys;
    const vector<double>& rs = frame_points_xyr.rs;

    // 创建1280x800的黑色图像
    Mat image = Mat::zeros(800, 1280, CV_8UC3);

    char idx_text[20];
    for (size_t ii = 0; ii < xs.size(); ii++) {
        double r = rs[ii];

        // 绘制内圆
        circle(image, cv::Point((int)xs[ii], (int)ys[ii]), (int)r, Scalar(0, 128, 255), 1);
        // 绘制外圆
        circle(image, cv::Point((int)xs[ii], (int)ys[ii]), (int)(r * 2), Scalar(0, 64, 128), 1);

        // 添加索引标签
        snprintf(idx_text, 20, "%d", (int)ii);
        putText(image, idx_text, cv::Point((int)xs[ii] + (int)r, (int)ys[ii] + (int)r),
                FONT_HERSHEY_SIMPLEX, 0.4, Scalar(0, 128, 255), 1);
    }

    // 在图像上添加帧信息
    char frame_text[50];
    snprintf(frame_text, 50, "Frame: %d, Blobs: %d", frame_index, (int)xs.size());
    putText(image, frame_text, cv::Point(10, 30), FONT_HERSHEY_SIMPLEX, 0.8, Scalar(255, 255, 255), 2);

    return image;
}

void printUsage(const char* programName) {
    cout << "Usage: " << programName << " <input_file> [output_folder]" << endl;
    cout << "  input_file: Path to the fpgabuf text file" << endl;
    cout << "  output_folder: Output folder for images (default: output_images)" << endl;
    cout << "Example: " << programName << " demo/FpgaBuf_0.txt output_images" << endl;
}

int main(int argc, char* argv[]) {
    // 检查命令行参数
    if (argc < 2) {
        printUsage(argv[0]);
        return 1;
    }

    string input_file = argv[1];
    string output_folder = (argc >= 3) ? argv[2] : "output_images";

    // 检查输入文件是否存在
    if (!fs::exists(input_file)) {
        cerr << "Error: Input file '" << input_file << "' does not exist!" << endl;
        return 1;
    }

    // 创建输出文件夹
    try {
        if (fs::exists(output_folder)) {
            cout << "Output folder '" << output_folder << "' already exists. Images will be added to it." << endl;
        } else {
            fs::create_directories(output_folder);
            cout << "Created output folder: " << output_folder << endl;
        }
    } catch (const fs::filesystem_error& ex) {
        cerr << "Error creating output folder: " << ex.what() << endl;
        return 1;
    }

    // 打开输入文件
    ifstream input_stream(input_file);
    if (!input_stream.is_open()) {
        cerr << "Error: Cannot open input file '" << input_file << "'" << endl;
        return 1;
    }

    cout << "Processing file: " << input_file << endl;
    cout << "Output folder: " << output_folder << endl;

    string line;
    char buffer[10000];
    int frame_index = 0;
    int processed_frames = 0;
    int error_frames = 0;

    while (getline(input_stream, line)) {
        if (line.empty()) {
            continue;
        }

        try {
            // 将文本行转换为字节缓冲区
            int buffer_size = line2buffer(line, buffer);

            if (buffer_size <= 8) {
                cout << "Warning: Frame " << frame_index << " has insufficient data (size: " << buffer_size << ")" << endl;
                error_frames++;
                frame_index++;
                continue;
            }

            // 解析FPGA缓冲区数据
            LED_ALG_NAMESPACE::MarkerBlobFrame marker_blob_frame;
            bool success = fpga_util::ReadFPGABuffer(buffer, 0, buffer_size, marker_blob_frame);

            if (!success) {
                cout << "Warning: Failed to parse frame " << frame_index << endl;
                error_frames++;
                frame_index++;
                continue;
            }

            // 转换为XYR格式
            fpga_util::FramePointsXYR frame_points_xyr;
            fpga_util::MarkerBlobFrame2XYR(marker_blob_frame, frame_points_xyr);

            // 生成图像
            Mat image = DrawBlobs(frame_points_xyr, frame_index);

            // 保存图像
            char output_filename[256];
            snprintf(output_filename, 256, "%s/frame_%04d.png", output_folder.c_str(), frame_index);

            if (imwrite(output_filename, image)) {
                processed_frames++;
                if (processed_frames % 10 == 0) {
                    cout << "Processed " << processed_frames << " frames..." << endl;
                }
            } else {
                cerr << "Error: Failed to save image " << output_filename << endl;
                error_frames++;
            }

            // 清理内存
            if (marker_blob_frame.marker_blobs != nullptr) {
                free(marker_blob_frame.marker_blobs);
            }

        } catch (const exception& e) {
            cerr << "Error processing frame " << frame_index << ": " << e.what() << endl;
            error_frames++;
        }

        frame_index++;
    }

    input_stream.close();

    // 输出处理结果
    cout << "\n=== Processing Complete ===" << endl;
    cout << "Total frames processed: " << processed_frames << endl;
    cout << "Error frames: " << error_frames << endl;
    cout << "Images saved to: " << output_folder << endl;

    if (processed_frames > 0) {
        cout << "Success! Generated " << processed_frames << " images." << endl;
        return 0;
    } else {
        cerr << "No frames were successfully processed!" << endl;
        return 1;
    }
}
