@echo off
echo Testing fpgabuf_to_img...

REM 检查可执行文件是否存在
if exist "build\bin\Release\fpgabuf_to_img.exe" (
    set EXECUTABLE=build\bin\Release\fpgabuf_to_img.exe
) else if exist "build\fpgabuf_to_img.exe" (
    set EXECUTABLE=build\fpgabuf_to_img.exe
) else if exist "fpgabuf_to_img.exe" (
    set EXECUTABLE=fpgabuf_to_img.exe
) else (
    echo Error: fpgabuf_to_img.exe not found!
    echo Please build the project first using build.bat
    pause
    exit /b 1
)

echo Found executable: %EXECUTABLE%

REM 检查demo文件是否存在
if not exist "demo\FpgaBuf_0.txt" (
    echo Error: demo\FpgaBuf_0.txt not found!
    echo Please make sure the demo folder exists with sample files.
    pause
    exit /b 1
)

echo Testing with demo\FpgaBuf_0.txt...

REM 运行程序
%EXECUTABLE% demo\FpgaBuf_0.txt test_output

REM 检查是否成功
if %errorlevel% neq 0 (
    echo Test failed!
    pause
    exit /b 1
)

echo Test completed successfully!
echo Check the test_output folder for generated images.

REM 显示生成的文件数量
if exist "test_output" (
    dir /b test_output\*.png > temp_count.txt 2>nul
    for /f %%i in ('type temp_count.txt ^| find /c /v ""') do set count=%%i
    del temp_count.txt 2>nul
    echo Generated %count% images.
) else (
    echo Warning: test_output folder not found!
)

pause
