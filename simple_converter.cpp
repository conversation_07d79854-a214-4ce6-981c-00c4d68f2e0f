#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <filesystem>
#include <sstream>
#include <iomanip>
#include "fpgaUtil/fpga_util.h"

using namespace std;
namespace fs = std::filesystem;

// 将一行文本数据转换为字节缓冲区
int line2buffer(const string& line, char* buffer) {
    uint8_t ch = 0;
    int count = 0;
    
    for (size_t i = 0; i < line.size(); i++) {
        char c = line[i];
        if (c == ' ') {
            buffer[count] = (char)ch;
            count++;
            ch = 0;
        } else if (c >= '0' && c <= '9') {
            ch = ch * 10 + c - '0';
        }
    }
    
    // 处理最后一个数字（如果行末没有空格）
    if (ch != 0) {
        buffer[count] = (char)ch;
        count++;
    }
    
    return count;
}

// 生成SVG格式的图像文件
void generateSVG(const fpga_util::FramePointsXYR& frame_points_xyr, int frame_index, const string& output_path) {
    const vector<double>& xs = frame_points_xyr.xs;
    const vector<double>& ys = frame_points_xyr.ys;
    const vector<double>& rs = frame_points_xyr.rs;
    
    ofstream svg_file(output_path);
    if (!svg_file.is_open()) {
        cerr << "Error: Cannot create SVG file " << output_path << endl;
        return;
    }
    
    // SVG头部
    svg_file << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" << endl;
    svg_file << "<svg width=\"1280\" height=\"800\" xmlns=\"http://www.w3.org/2000/svg\">" << endl;
    svg_file << "<rect width=\"100%\" height=\"100%\" fill=\"black\"/>" << endl;
    
    // 绘制blob
    for (size_t i = 0; i < xs.size(); i++) {
        double x = xs[i];
        double y = ys[i];
        double r = rs[i];
        
        // 内圆
        svg_file << "<circle cx=\"" << x << "\" cy=\"" << y << "\" r=\"" << r 
                 << "\" fill=\"none\" stroke=\"#0080FF\" stroke-width=\"1\"/>" << endl;
        
        // 外圆
        svg_file << "<circle cx=\"" << x << "\" cy=\"" << y << "\" r=\"" << (r * 2) 
                 << "\" fill=\"none\" stroke=\"#004080\" stroke-width=\"1\"/>" << endl;
        
        // 标签
        svg_file << "<text x=\"" << (x + r) << "\" y=\"" << (y + r) 
                 << "\" fill=\"#0080FF\" font-family=\"Arial\" font-size=\"12\">" 
                 << i << "</text>" << endl;
    }
    
    // 添加帧信息
    svg_file << "<text x=\"10\" y=\"30\" fill=\"white\" font-family=\"Arial\" font-size=\"16\">"
             << "Frame: " << frame_index << ", Blobs: " << xs.size() << "</text>" << endl;
    
    svg_file << "</svg>" << endl;
    svg_file.close();
}

// 生成HTML报告
void generateHTMLReport(const string& output_folder, int total_frames, int processed_frames, int error_frames) {
    string html_path = output_folder + "/report.html";
    ofstream html_file(html_path);
    
    if (!html_file.is_open()) {
        cerr << "Warning: Cannot create HTML report" << endl;
        return;
    }
    
    html_file << "<!DOCTYPE html>" << endl;
    html_file << "<html><head><title>FPGA Buffer Conversion Report</title>" << endl;
    html_file << "<style>" << endl;
    html_file << "body { font-family: Arial, sans-serif; margin: 20px; }" << endl;
    html_file << ".summary { background: #f0f0f0; padding: 10px; border-radius: 5px; }" << endl;
    html_file << ".image-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; }" << endl;
    html_file << ".image-item { border: 1px solid #ccc; padding: 10px; text-align: center; }" << endl;
    html_file << "svg { max-width: 100%; height: auto; }" << endl;
    html_file << "</style></head><body>" << endl;
    
    html_file << "<h1>FPGA Buffer to Image Conversion Report</h1>" << endl;
    html_file << "<div class=\"summary\">" << endl;
    html_file << "<h2>Summary</h2>" << endl;
    html_file << "<p>Total frames: " << total_frames << "</p>" << endl;
    html_file << "<p>Successfully processed: " << processed_frames << "</p>" << endl;
    html_file << "<p>Errors: " << error_frames << "</p>" << endl;
    html_file << "<p>Success rate: " << fixed << setprecision(1) 
              << (total_frames > 0 ? (double)processed_frames / total_frames * 100 : 0) << "%</p>" << endl;
    html_file << "</div>" << endl;
    
    html_file << "<h2>Generated Images</h2>" << endl;
    html_file << "<div class=\"image-grid\">" << endl;
    
    // 列出所有SVG文件
    for (int i = 0; i < processed_frames && i < 50; i++) { // 限制显示前50个
        string svg_filename = "frame_" + string(4 - to_string(i).length(), '0') + to_string(i) + ".svg";
        html_file << "<div class=\"image-item\">" << endl;
        html_file << "<h3>Frame " << i << "</h3>" << endl;
        html_file << "<object data=\"" << svg_filename << "\" type=\"image/svg+xml\" width=\"320\" height=\"200\"></object>" << endl;
        html_file << "</div>" << endl;
    }
    
    if (processed_frames > 50) {
        html_file << "<p>... and " << (processed_frames - 50) << " more images</p>" << endl;
    }
    
    html_file << "</div>" << endl;
    html_file << "</body></html>" << endl;
    html_file.close();
    
    cout << "HTML report generated: " << html_path << endl;
}

void printUsage(const char* programName) {
    cout << "Usage: " << programName << " <input_file> [output_folder]" << endl;
    cout << "  input_file: Path to the fpgabuf text file" << endl;
    cout << "  output_folder: Output folder for SVG images (default: output_images)" << endl;
    cout << "Example: " << programName << " demo/FpgaBuf_0.txt output_images" << endl;
    cout << "Note: This version generates SVG files instead of PNG (no OpenCV dependency)" << endl;
}

int main(int argc, char* argv[]) {
    // 检查命令行参数
    if (argc < 2) {
        printUsage(argv[0]);
        return 1;
    }
    
    string input_file = argv[1];
    string output_folder = (argc >= 3) ? argv[2] : "output_images";
    
    // 检查输入文件是否存在
    if (!fs::exists(input_file)) {
        cerr << "Error: Input file '" << input_file << "' does not exist!" << endl;
        return 1;
    }
    
    // 创建输出文件夹
    try {
        if (fs::exists(output_folder)) {
            cout << "Output folder '" << output_folder << "' already exists. Images will be added to it." << endl;
        } else {
            fs::create_directories(output_folder);
            cout << "Created output folder: " << output_folder << endl;
        }
    } catch (const fs::filesystem_error& ex) {
        cerr << "Error creating output folder: " << ex.what() << endl;
        return 1;
    }
    
    // 打开输入文件
    ifstream input_stream(input_file);
    if (!input_stream.is_open()) {
        cerr << "Error: Cannot open input file '" << input_file << "'" << endl;
        return 1;
    }
    
    cout << "Processing file: " << input_file << endl;
    cout << "Output folder: " << output_folder << endl;
    cout << "Output format: SVG (Scalable Vector Graphics)" << endl;
    
    string line;
    char buffer[10000];
    int frame_index = 0;
    int processed_frames = 0;
    int error_frames = 0;
    
    while (getline(input_stream, line)) {
        if (line.empty()) {
            continue;
        }
        
        try {
            // 将文本行转换为字节缓冲区
            int buffer_size = line2buffer(line, buffer);
            
            if (buffer_size <= 8) {
                cout << "Warning: Frame " << frame_index << " has insufficient data (size: " << buffer_size << ")" << endl;
                error_frames++;
                frame_index++;
                continue;
            }
            
            // 解析FPGA缓冲区数据
            LED_ALG_NAMESPACE::MarkerBlobFrame marker_blob_frame;
            bool success = fpga_util::ReadFPGABuffer(buffer, 0, buffer_size, marker_blob_frame);
            
            if (!success) {
                cout << "Warning: Failed to parse frame " << frame_index << endl;
                error_frames++;
                frame_index++;
                continue;
            }
            
            // 转换为XYR格式
            fpga_util::FramePointsXYR frame_points_xyr;
            fpga_util::MarkerBlobFrame2XYR(marker_blob_frame, frame_points_xyr);
            
            // 生成SVG文件
            stringstream ss;
            ss << output_folder << "/frame_" << setfill('0') << setw(4) << frame_index << ".svg";
            string output_filename = ss.str();
            
            generateSVG(frame_points_xyr, frame_index, output_filename);
            processed_frames++;
            
            if (processed_frames % 10 == 0) {
                cout << "Processed " << processed_frames << " frames..." << endl;
            }
            
            // 清理内存
            if (marker_blob_frame.marker_blobs != nullptr) {
                free(marker_blob_frame.marker_blobs);
            }
            
        } catch (const exception& e) {
            cerr << "Error processing frame " << frame_index << ": " << e.what() << endl;
            error_frames++;
        }
        
        frame_index++;
    }
    
    input_stream.close();
    
    // 生成HTML报告
    generateHTMLReport(output_folder, frame_index, processed_frames, error_frames);
    
    // 输出处理结果
    cout << "\n=== Processing Complete ===" << endl;
    cout << "Total frames processed: " << processed_frames << endl;
    cout << "Error frames: " << error_frames << endl;
    cout << "Images saved to: " << output_folder << endl;
    cout << "Format: SVG (can be viewed in web browsers)" << endl;
    
    if (processed_frames > 0) {
        cout << "Success! Generated " << processed_frames << " SVG images." << endl;
        cout << "Open " << output_folder << "/report.html in a web browser to view all images." << endl;
        return 0;
    } else {
        cerr << "No frames were successfully processed!" << endl;
        return 1;
    }
}
