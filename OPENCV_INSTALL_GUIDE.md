# OpenCV 安装指南

## 🚀 推荐安装方法

### 方法1：使用简化版本（无需OpenCV）
**最简单的方法，推荐首选！**

```bash
# 直接使用简化版本，无需安装OpenCV
build_simple.bat
```

简化版本生成SVG格式图像，功能完整且无依赖。

### 方法2：自动安装OpenCV（vcpkg）

```bash
# 运行自动安装脚本
install_opencv.bat
```

这将：
- 自动下载并构建vcpkg
- 安装OpenCV及其依赖
- 配置CMake工具链

### 方法3：使用Chocolatey安装

```bash
# 以管理员身份运行
install_opencv_choco.bat
```

### 方法4：手动安装

```bash
# 查看手动安装指南
install_opencv_manual.bat
```

## 📋 详细安装步骤

### 使用vcpkg安装（推荐）

1. **运行安装脚本**：
   ```bash
   install_opencv.bat
   ```

2. **等待安装完成**（可能需要30-60分钟）

3. **构建项目**：
   ```bash
   build.bat
   ```

### 手动安装步骤

1. **下载OpenCV**：
   - 访问 https://opencv.org/releases/
   - 下载Windows版本（如opencv-4.8.0-windows.exe）

2. **解压安装**：
   - 解压到 `C:\opencv`

3. **设置环境变量**：
   - `OPENCV_DIR` = `C:\opencv\build`
   - 添加到PATH: `C:\opencv\build\x64\vc15\bin`

4. **构建项目**：
   ```bash
   build.bat
   ```

## 🔧 故障排除

### 问题1：CMake找不到OpenCV

**解决方案**：
```bash
# 设置OpenCV路径
set OpenCV_DIR=C:\opencv\build
# 或者使用vcpkg
set CMAKE_TOOLCHAIN_FILE=tools\vcpkg\scripts\buildsystems\vcpkg.cmake
```

### 问题2：编译器未找到

**解决方案**：
- 安装Visual Studio Community 2019/2022
- 或安装Visual Studio Build Tools
- 从"开发者命令提示符"运行构建脚本

### 问题3：vcpkg安装失败

**解决方案**：
```bash
# 手动安装依赖
choco install git cmake visualstudio2019buildtools -y
# 然后重新运行
install_opencv.bat
```

### 问题4：权限错误

**解决方案**：
- 以管理员身份运行命令提示符
- 或选择用户目录进行安装

## 🎯 快速解决方案

如果OpenCV安装遇到困难，**强烈推荐使用简化版本**：

```bash
# 1. 构建简化版本（无需OpenCV）
build_simple.bat

# 2. 测试程序
test_simple.bat

# 3. 使用程序
simple_converter.exe demo\FpgaBuf_0.txt output
```

简化版本的优势：
- ✅ 无需安装任何依赖
- ✅ 生成SVG矢量图像
- ✅ 自动生成HTML报告
- ✅ 功能完整，满足大部分需求

## 📞 获取帮助

如果仍然遇到问题：

1. **检查系统要求**：
   - Windows 10/11
   - Visual Studio 2019/2022 或 MinGW
   - 至少4GB可用磁盘空间

2. **查看错误信息**：
   - 记录完整的错误消息
   - 检查是否缺少依赖

3. **尝试替代方案**：
   - 使用简化版本
   - 使用不同的安装方法

## 🔄 安装验证

安装完成后，验证是否成功：

```bash
# 检查OpenCV是否可用
cmake --find-package -DNAME=OpenCV -DCOMPILER_ID=GNU -DLANGUAGE=C -DMODE=EXIST

# 或者直接构建项目
build.bat
```

成功的标志：
- CMake配置无错误
- 编译成功生成可执行文件
- 测试脚本运行正常

---

**建议：如果您只是想快速使用程序，请直接使用简化版本，它无需安装OpenCV即可正常工作。**
