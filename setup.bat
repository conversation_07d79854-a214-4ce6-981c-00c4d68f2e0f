@echo off
title FPGA Buffer to Image Converter - Setup
color 0A

echo.
echo ========================================
echo  FPGA Buffer to Image Converter Setup
echo ========================================
echo.
echo Choose your installation option:
echo.
echo [1] Quick Setup (Recommended)
echo     - Build simple converter (no dependencies)
echo     - Generate SVG images
echo     - Ready to use immediately
echo.
echo [2] Full Setup with OpenCV (vcpkg)
echo     - Auto-install OpenCV using vcpkg
echo     - Generate PNG images
echo     - Takes 30-60 minutes
echo.
echo [3] Full Setup with OpenCV (Chocolatey)
echo     - Install OpenCV using Chocolatey
echo     - Requires admin privileges
echo     - Faster installation
echo.
echo [4] Setup for Scoop OpenCV Installation
echo     - Configure existing scoop OpenCV
echo     - For users who installed via scoop
echo.
echo [5] Manual OpenCV Installation Guide
echo     - Show manual installation steps
echo     - For advanced users
echo.
echo [6] Test OpenCV Installation
echo     - Verify OpenCV is properly installed
echo.
echo [7] Exit
echo.

set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto quick_setup
if "%choice%"=="2" goto vcpkg_setup
if "%choice%"=="3" goto choco_setup
if "%choice%"=="4" goto scoop_setup
if "%choice%"=="5" goto manual_guide
if "%choice%"=="6" goto test_opencv
if "%choice%"=="7" goto exit
goto invalid_choice

:quick_setup
echo.
echo ========================================
echo  Quick Setup (Simple Converter)
echo ========================================
echo.
echo Building simple converter...
call build_simple.bat
if %errorlevel% neq 0 (
    echo Build failed!
    pause
    goto menu
)

echo.
echo Testing the converter...
call test_simple.bat
if %errorlevel% neq 0 (
    echo Test failed!
    pause
    goto menu
)

echo.
echo ========================================
echo  Quick Setup Complete!
echo ========================================
echo.
echo Your converter is ready to use:
echo   simple_converter.exe input_file.txt output_folder
echo.
echo Example:
echo   simple_converter.exe demo\FpgaBuf_0.txt my_output
echo.
echo The test created images in: test_output_svg
echo Open test_output_svg\report.html in a browser to view results.
echo.
pause
goto menu

:vcpkg_setup
echo.
echo ========================================
echo  Full Setup with vcpkg
echo ========================================
echo.
echo This will:
echo - Download and build vcpkg
echo - Install OpenCV and dependencies
echo - Build the full converter
echo.
echo This may take 30-60 minutes. Continue? (Y/N)
set /p confirm="Enter Y to continue: "
if /i not "%confirm%"=="Y" goto menu

echo Installing OpenCV via vcpkg...
call install_opencv.bat
if %errorlevel% neq 0 (
    echo OpenCV installation failed!
    echo You can try the quick setup instead.
    pause
    goto menu
)

echo Building full converter...
call build.bat
if %errorlevel% neq 0 (
    echo Build failed! Trying simple converter instead...
    call build_simple.bat
)

echo Testing...
call test.bat
if %errorlevel% neq 0 (
    call test_simple.bat
)

echo.
echo ========================================
echo  Full Setup Complete!
echo ========================================
echo.
pause
goto menu

:choco_setup
echo.
echo ========================================
echo  Full Setup with Chocolatey
echo ========================================
echo.
echo This requires administrator privileges.
echo Make sure you're running as administrator.
echo.
echo Continue? (Y/N)
set /p confirm="Enter Y to continue: "
if /i not "%confirm%"=="Y" goto menu

call install_opencv_choco.bat
if %errorlevel% neq 0 (
    echo Installation failed!
    pause
    goto menu
)

echo Building full converter...
call build.bat
if %errorlevel% neq 0 (
    echo Build failed! Trying simple converter instead...
    call build_simple.bat
)

echo.
echo ========================================
echo  Setup Complete!
echo ========================================
echo.
pause
goto menu

:scoop_setup
echo.
echo ========================================
echo  Setup for Scoop OpenCV Installation
echo ========================================
echo.
echo Setting up environment for scoop-installed OpenCV...
call setup_opencv_scoop.bat
if %errorlevel% neq 0 (
    echo Setup failed!
    pause
    goto menu
)

echo.
echo ========================================
echo  Scoop OpenCV Setup Complete!
echo ========================================
echo.
pause
goto menu

:manual_guide
echo.
echo ========================================
echo  Manual Installation Guide
echo ========================================
echo.
call install_opencv_manual.bat
echo.
echo For detailed instructions, see: OPENCV_INSTALL_GUIDE.md
echo.
pause
goto menu

:test_opencv
echo.
echo ========================================
echo  Testing OpenCV Installation
echo ========================================
echo.
call test_opencv.bat
pause
goto menu

:invalid_choice
echo.
echo Invalid choice! Please enter 1-7.
echo.
pause

:menu
cls
goto :eof

:exit
echo.
echo Thank you for using FPGA Buffer to Image Converter!
echo.
pause
exit /b 0
