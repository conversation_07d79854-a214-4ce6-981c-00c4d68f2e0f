@echo off
echo Installing OpenCV using Chocolatey...

REM 检查是否已安装Chocolatey
where choco >nul 2>&1
if %errorlevel% neq 0 (
    echo Chocolatey not found. Installing Chocolatey first...
    echo.
    echo This will install Chocolatey package manager.
    echo Press any key to continue or Ctrl+C to cancel...
    pause >nul
    
    REM 安装Chocolatey
    powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))"
    
    if %errorlevel% neq 0 (
        echo Failed to install Chocolatey
        echo Please install manually from: https://chocolatey.org/install
        pause
        exit /b 1
    )
    
    echo Chocolatey installed successfully!
    echo Please restart this script or open a new command prompt.
    pause
    exit /b 0
)

echo Chocolatey found. Installing OpenCV...

REM 安装OpenCV
choco install opencv -y

if %errorlevel% neq 0 (
    echo Failed to install OpenCV via Chocolatey
    echo Trying alternative method...
    
    REM 尝试安装开发工具
    choco install cmake -y
    choco install git -y
    choco install visualstudio2019buildtools -y
    
    echo Please try running install_opencv.bat for vcpkg installation
    pause
    exit /b 1
)

echo OpenCV installed successfully via Chocolatey!
echo.
echo You may need to set environment variables:
echo - Add OpenCV bin directory to PATH
echo - Set OPENCV_DIR if needed
echo.
echo Try running build.bat now.
pause
