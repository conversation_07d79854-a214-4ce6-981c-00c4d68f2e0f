@echo off
echo Manual OpenCV Installation Guide
echo.
echo Please follow these steps to install OpenCV manually:
echo.
echo 1. Download OpenCV from: https://opencv.org/releases/
echo    - Choose the Windows version (e.g., opencv-4.8.0-windows.exe)
echo.
echo 2. Extract to a folder like: C:\opencv
echo.
echo 3. Add to system environment variables:
echo    - OPENCV_DIR = C:\opencv\build
echo    - Add to PATH: C:\opencv\build\x64\vc15\bin (or vc16 for newer VS)
echo.
echo 4. For CMake, you may need to set:
echo    - OpenCV_DIR = C:\opencv\build
echo.
echo After installation, try running build.bat again.
echo.
echo Alternative: Use the automated vcpkg installer by running install_opencv.bat
echo.
pause
