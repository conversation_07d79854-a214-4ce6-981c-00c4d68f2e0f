#include<iostream>
#include<fstream>
#include <opencv2/core/core.hpp>
#include <opencv2/highgui/highgui.hpp>
#include <opencv2/imgproc/imgproc.hpp>
#include "fpga_util.h"

using namespace std;
using namespace cv;

int hextochar(char * hex, int hex_size, char * buffer) 
{
    uint8_t ch = 0;
    int count =0;
	int hex_count = 0;
	
    for (int i = 0; i< hex_size; i++){
        char hex_ch = hex[i];
		bool flag = false;
        if (hex_ch >= '0' && hex_ch <= '9'){
            ch += hex_ch - '0';			
			flag = true;
        }
        else if (hex_ch >= 'A' && hex_ch <= 'F'){
            ch += hex_ch - 'A' + 10;
			flag = true;
        }

		if (flag) {
			if (hex_count % 2 == 1) {
				buffer[count] = (char)ch;
				count++;
				ch = 0;
			}
			else {
				ch *= 16;
			}
			hex_count++;
		}

    }
	return count;
}

int line2buffer(string line, char * buffer){
	uint8_t ch = 0;
	char* line_arr = (char*)line.c_str();
	int count = 0;
	for (int i =0;i< line.size(); i++){
		char c = line_arr[i];
		if (c == ' '){
			buffer[count] = (char)ch;
			count++;
			ch = 0;
		}else if(c>= '0' && c <= '9'){
			ch = ch *10 + c -'0';
		}
	}
	return count;
} 

// display blobs
Mat DrawBlobs(fpga_util::FramePointsXYR frame_points_xyr)
{
	vector<double> xs =  frame_points_xyr.xs;
	vector<double> ys =  frame_points_xyr.ys;
	vector<double> rs =  frame_points_xyr.rs;

	Mat image = Mat::zeros(800, 1280, CV_8UC3);

	char idx_text[20];
	double r = 2;
	bool with_radius = true, required_idx = true;
	for (int ii = 0; ii < xs.size(); ii++) {
		if (with_radius) {
			r = rs[ii];
			circle(image, cv::Point(xs[ii], ys[ii]), rs[ii], Scalar(0, 128, 255), 1);
			circle(image, cv::Point(xs[ii], ys[ii]), rs[ii] * 2, Scalar(0, 64, 128), 1);
		}
		else
			circle(image, cv::Point(xs[ii], ys[ii]), r, Scalar(0, 255, 255), r);
		if (required_idx) {
			snprintf(idx_text, 20, "%d", ii);
			putText(image, idx_text, cv::Point(xs[ii] + r, ys[ii] + r), FONT_HERSHEY_SIMPLEX, 0.4, Scalar(0, 128, 255), 1);
		}
	}

	return image;
}


int main(){
    // printf("Hello world. \n");
    string line;
    char buffer[10000];
	int file_ids[6] = { 0, 19, 38, 57, 76, 95 };


	for (int i_file = 0; i_file < 1; i_file++) {
		char log_file_path[200];
		//sprintf(log_file_path, "..//data//FpgaBuf_%d.txt", file_ids[i_file]);
		//snprintf(log_file_path,200,  "..//data//20220623_180243_FPGAData//FPGAData//FpgaBuf_%d.txt", i_file+1);
		snprintf(log_file_path, 200, "FpgaBuf.txt");
		ifstream myfile(log_file_path);
		char ch;
		int wait_time = 0;
		if (myfile.is_open())
		{
			LED_ALG_NAMESPACE::MarkerBlobFrame marker_blob_frame;

			while (getline(myfile, line))
			{

				if (line.size() > 0) {
					// string head_str = line.substr(0, 5);
					// string msg_str = line.substr(6, line.size() - 7);
					// cout << head_str << endl;
					// cout << msg_str<<endl;
					// int buffer_size = hextochar((char*)(msg_str.c_str()), msg_str.size(), buffer);
					int buffer_size = line2buffer(line, buffer);
					cout << "buffer_size: " << buffer_size << endl;
					fpga_util::ReadFPGABuffer(buffer, 0, buffer_size, marker_blob_frame);
					cout << "blob_num: " << marker_blob_frame.blob_num << endl;
					for (int ii = 0; ii < marker_blob_frame.blob_num; ii++) {
						cout << "#" << ii << ", pid, id, color, xmin, xmax, ymin, ymax:";
						cout << marker_blob_frame.marker_blobs[ii].root_id << ' ';
						cout << marker_blob_frame.marker_blobs[ii].label_id << ' ';
						cout << marker_blob_frame.marker_blobs[ii].color_flag << ' ';
						cout << marker_blob_frame.marker_blobs[ii].x_min << ' ';
						cout << marker_blob_frame.marker_blobs[ii].x_max << ' ';
						cout << marker_blob_frame.marker_blobs[ii].y_min << ' ';
						cout << marker_blob_frame.marker_blobs[ii].y_max << ' ';
						cout << endl;
					}
					fpga_util::FramePointsXYR frame_points_xyr;
					fpga_util::MarkerBlobFrame2XYR(marker_blob_frame, frame_points_xyr);
					Mat image_blob = DrawBlobs(frame_points_xyr);

					imshow("blobs", image_blob);
					imshow("blobs2", image_blob);
					imshow("blobs3", image_blob);
					char c = waitKey(wait_time);


					if (marker_blob_frame.marker_blobs != NULL)
						free(marker_blob_frame.marker_blobs);
					if (c == 'p') {
						wait_time = (wait_time + 1) % 2;
					}
					if (c == 'r') {
						break;
					}
					//cin.get();
				}
				else {
					cout << endl;
				}



			}
			myfile.close();

		}
		else {
			cout << "Cannot open file." << endl;
		}
		waitKey(60000);
	}
    return 0;
}