@echo off
echo Testing OpenCV installation...

set OPENCV_ROOT=C:\App\scoop\apps\opencv\current
set OPENCV_BUILD=%OPENCV_ROOT%\build
set OPENCV_BIN=%OPENCV_ROOT%\x64\vc16\bin

echo.
echo ========================================
echo  OpenCV Installation Test
echo ========================================
echo.

REM 检查目录结构
echo Checking directory structure...
if exist "%OPENCV_ROOT%" (
    echo ✓ OpenCV root directory found: %OPENCV_ROOT%
) else (
    echo ✗ OpenCV root directory not found: %OPENCV_ROOT%
    goto error
)

if exist "%OPENCV_BUILD%" (
    echo ✓ OpenCV build directory found: %OPENCV_BUILD%
) else (
    echo ✗ OpenCV build directory not found: %OPENCV_BUILD%
    goto error
)

if exist "%OPENCV_BIN%" (
    echo ✓ OpenCV bin directory found: %OPENCV_BIN%
) else (
    echo ✗ OpenCV bin directory not found: %OPENCV_BIN%
    goto error
)

REM 检查关键文件
echo.
echo Checking key files...

if exist "%OPENCV_BUILD%\OpenCVConfig.cmake" (
    echo ✓ OpenCVConfig.cmake found
) else (
    echo ✗ OpenCVConfig.cmake not found
    echo   This file is required for CMake to find OpenCV
)

if exist "%OPENCV_BIN%\opencv_world*.dll" (
    echo ✓ OpenCV world DLL found
    set OPENCV_DLL_FOUND=1
) else (
    echo ⚠ OpenCV world DLL not found, checking individual DLLs...
    if exist "%OPENCV_BIN%\opencv_core*.dll" (
        echo ✓ OpenCV core DLL found
        set OPENCV_DLL_FOUND=1
    ) else (
        echo ✗ OpenCV DLLs not found
        set OPENCV_DLL_FOUND=0
    )
)

REM 检查头文件
if exist "%OPENCV_BUILD%\include\opencv2\opencv.hpp" (
    echo ✓ OpenCV headers found
) else (
    echo ✗ OpenCV headers not found
    echo   Headers are required for compilation
)

REM 检查库文件
if exist "%OPENCV_BUILD%\x64\vc16\lib\opencv_world*.lib" (
    echo ✓ OpenCV libraries found
) else (
    echo ⚠ OpenCV world library not found, checking individual libraries...
    if exist "%OPENCV_BUILD%\x64\vc16\lib\opencv_core*.lib" (
        echo ✓ OpenCV core library found
    ) else (
        echo ✗ OpenCV libraries not found
    )
)

echo.
echo ========================================
echo  Test Results
echo ========================================
echo.

if %OPENCV_DLL_FOUND%==1 (
    echo ✓ OpenCV installation appears to be complete
    echo.
    echo Environment setup:
    echo   OpenCV_DIR should be set to: %OPENCV_BUILD%
    echo   PATH should include: %OPENCV_BIN%
    echo.
    echo You can now try building the project:
    echo   1. Run: setup_opencv_scoop.bat
    echo   2. Then: build.bat
    echo.
    echo Or use the simple version (no OpenCV needed):
    echo   build_simple.bat
) else (
    echo ✗ OpenCV installation is incomplete
    echo.
    echo Please reinstall OpenCV:
    echo   scoop uninstall opencv
    echo   scoop install opencv
)

pause
exit /b 0

:error
echo.
echo ✗ OpenCV not found at expected location
echo.
echo Please check your scoop installation:
echo   scoop list opencv
echo   scoop install opencv
echo.
echo If OpenCV is installed elsewhere, please update the paths in:
echo   - build.bat
echo   - CMakeLists.txt
echo   - setup_opencv_scoop.bat
echo.
pause
exit /b 1
