@echo off
echo Building fpgabuf_to_img with scoop-installed OpenCV...

REM 设置OpenCV环境变量
set OPENCV_ROOT=C:\App\scoop\apps\opencv\current
set OPENCV_BUILD=%OPENCV_ROOT%\build
set OPENCV_BIN=%OPENCV_ROOT%\x64\vc16\bin
set OpenCV_DIR=%OPENCV_BUILD%
set PATH=%OPENCV_BIN%;%PATH%

echo Using OpenCV from: %OPENCV_ROOT%
echo OpenCV_DIR set to: %OpenCV_DIR%

REM 检查OpenCV是否存在
if not exist "%OPENCV_BUILD%" (
    echo Error: OpenCV build directory not found at %OPENCV_BUILD%
    echo Please make sure OpenCV is installed via scoop:
    echo   scoop install opencv
    pause
    exit /b 1
)

REM 检查是否存在build目录，如果不存在则创建
if not exist build mkdir build

REM 进入build目录
cd build

REM 运行CMake配置
echo Running CMake configuration with scoop OpenCV...
cmake .. -G "Visual Studio 16 2019" -A x64 -DOpenCV_DIR="%OpenCV_DIR%"

REM 如果CMake配置失败，尝试其他生成器
if %errorlevel% neq 0 (
    echo Trying with Visual Studio 17 2022...
    cmake .. -G "Visual Studio 17 2022" -A x64 -DOpenCV_DIR="%OpenCV_DIR%"
)

REM 如果仍然失败，尝试MinGW
if %errorlevel% neq 0 (
    echo Trying with MinGW Makefiles...
    cmake .. -G "MinGW Makefiles" -DOpenCV_DIR="%OpenCV_DIR%"
)

REM 如果仍然失败，提示用户
if %errorlevel% neq 0 (
    echo CMake configuration failed!
    echo.
    echo Please check:
    echo 1. OpenCV is properly installed via scoop
    echo 2. CMake is installed and in PATH
    echo 3. Visual Studio or MinGW is installed
    echo.
    echo You can also try the simple version (no OpenCV needed):
    echo   build_simple.bat
    pause
    exit /b 1
)

REM 构建项目
echo Building project...
cmake --build . --config Release

REM 检查构建是否成功
if %errorlevel% neq 0 (
    echo Build failed!
    echo.
    echo Trying to build simple version instead...
    cd ..
    call build_simple.bat
    pause
    exit /b 1
)

echo Build successful!
echo.

REM 检查可执行文件
if exist "bin\Release\fpgabuf_to_img.exe" (
    echo Executable location: build\bin\Release\fpgabuf_to_img.exe
    set EXECUTABLE=bin\Release\fpgabuf_to_img.exe
) else if exist "fpgabuf_to_img.exe" (
    echo Executable location: build\fpgabuf_to_img.exe
    set EXECUTABLE=fpgabuf_to_img.exe
) else (
    echo Warning: Executable not found in expected locations
    set EXECUTABLE=""
)

REM 复制OpenCV DLLs到可执行文件目录
if not "%EXECUTABLE%"=="" (
    echo Copying OpenCV DLLs...
    for %%f in ("%OPENCV_BIN%\opencv_world*.dll") do (
        copy "%%f" "bin\Release\" >nul 2>&1
        copy "%%f" "." >nul 2>&1
    )
    
    REM 如果没有world DLL，复制其他DLLs
    if not exist "bin\Release\opencv_world*.dll" (
        if not exist "opencv_world*.dll" (
            echo Copying individual OpenCV DLLs...
            copy "%OPENCV_BIN%\opencv_core*.dll" "bin\Release\" >nul 2>&1
            copy "%OPENCV_BIN%\opencv_imgproc*.dll" "bin\Release\" >nul 2>&1
            copy "%OPENCV_BIN%\opencv_imgcodecs*.dll" "bin\Release\" >nul 2>&1
            copy "%OPENCV_BIN%\opencv_highgui*.dll" "bin\Release\" >nul 2>&1
            copy "%OPENCV_BIN%\opencv_core*.dll" "." >nul 2>&1
            copy "%OPENCV_BIN%\opencv_imgproc*.dll" "." >nul 2>&1
            copy "%OPENCV_BIN%\opencv_imgcodecs*.dll" "." >nul 2>&1
            copy "%OPENCV_BIN%\opencv_highgui*.dll" "." >nul 2>&1
        )
    )
)

cd ..

echo.
echo ========================================
echo  Build Complete!
echo ========================================
echo.
echo You can now test the program:
echo   test.bat
echo.
echo Or use it directly:
echo   %EXECUTABLE% demo\FpgaBuf_0.txt output_images
echo.
pause
