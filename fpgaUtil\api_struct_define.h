#pragma once
#define LED_ALG_NAMESPACE led_ctrl_algorithm_core

#define XIM_ALG_LOG

namespace LED_ALG_NAMESPACE {
const int NUM_CIRCLE_MARKERS = 15;
  const int NUM_SQUARE_MARKERS = 65;
  const int NUM_TOPOTAG_ALL = 128;																											// num of topotag markers, ID = 100-227 is topotag id
  const int NUM_MARKERS_ALL = 80;																												// num of all the markers of all types, ID = 0~79 is marker id
  const int NUM_GROUPS = 20;																														// all groups type with multi-markers, ID = 80~99 is group id
  const int NUM_RETURN_IDS = NUM_TOPOTAG_ALL + NUM_MARKERS_ALL + NUM_GROUPS;            // max num of return ids, => 0-NUM_MARKERS_ALL-1 for card, NUM_MARKERS_ALL-NUM_MARKERS_ALL+NUM_GROUPS-1 for maps & controllers, NUM_MARKERS_ALL+NUM_GROUPS - NUM_RETURN_IDS for topotags
  const int MAX_NUM_SUB_MARKERS_IN_GROUP = NUM_RETURN_IDS;															// max num of sub-markers in a group, group can be either a map or a controller
  const int MAX_NUM_RING_BUFFER_ELEMENT = 8;

  /*** log levels ***/
  enum LOGLevel {
    _LOG_ERROR = 0,    
    _LOG_INFO = 1,
    _LOG_DEBUG = 2,
    _LOG_WARN = 3,
    _LOG_VERBOSE = 4,
    _LOG_OFF = 5,
  };

  /*** product types ***/
  enum ProductType {
    XAGIA = 10
  };

  /*** device types ***/
  enum DeviceType {
    LED_CTRL_LEFT,    // left led controller
    LED_CTRL_RIGHT,    // right led controller
    LED_CTRL_LEFT_AND_RIGHT // left and right led controller
  };

  /*** data stream types (input hid buffer) ***/
  enum StreamType {    
    XAGIA_SLIDEIN_FPGA,                     // fpga data stream slide in
    XAGIA_AIO_FPGA,                         // fpga data stream all in one
    XAGIA_IMU,                              // imu  data stream
    FUSION_6DOF,
		HEAD_POSE,
    XAGIA_AIO_MARKERBLOB,  // MarkerBlobFrame, MarkerBlob[N]
  };

  /*** feature types, each feature has its feature levels ***/
  enum FeatureType {
    MARKER_PARAM,                           // marker definition params
    POS_SMOOTH,                             // level= -1 off (default), on otherwise, i.e. 0~10, the larger, the smoother
    CAMERA_ROT,                             // camera rotate flag, level = 0, 90, 180, 270
    MAX_ROTATION_ANGEL,                     // level = 50 (default),  on otherwise, i.e. 0~180 degree
    MAX_TRANS_DISTANCE,                     // level = 100 (default) mm, on otherwise, i.e. > 0
  };

  /*** API defines for event handling ***/
  enum EventType {
    RESULT_6_DOF,                           // 6-dof result, see Frame6DOF for definition
    RESULT_6_DOF_SMOOTH,                    // 6-dof smooth result, enabled by set POS_SMOOTH feature
		RESULT_6_DOF_FUSION_LEFT,								// fusion left controller 6dof result
		RESULT_6_DOF_FUSION_RIGHT,							// fusion right controller 6dof result
  };
  
  enum AlgMode {
    TRACKING,                           // tracking mode
    CALIBRATION,                        // controller calibration mode 
  };

  enum ButtonState {
    RELEASED,                               // button is released
    PRESSED                                 // button is pressed
  };

  // newly added for client-server, 2022-05-04
	enum SHARED_MEM_TYPE {
		READ = 0,
		WRITE = 1
	};

  struct ElementOrientation {
    float qx, qy, qz, qw;                                            
  };

	struct IMUAccelerate {
		float acc_x, acc_y, acc_z;
	};

	struct IMUGyroscope {
		float gyro_x, gyro_y, gyro_z;
	};

	struct ElementVector3 {
		float x, y, z;
	};

	struct OmegaMat4 {
		float data[4][4];
	};

	struct ElementIMU {
		ElementOrientation orientation;					// quaternion of 3dof
		IMUAccelerate acc;											// accelerator info
		IMUGyroscope gyro;											// gyroscope info
		ElementVector3 euler;										// euler angle of 3dof (x = roll, y = pitch, z = yaw)
	};

	struct FrameIMU {
		unsigned int controller_id;						  // controller id
		unsigned int sec;												// timestamp, sec
		unsigned int nsec;											// timestamp, nsec
		ElementIMU imu_data;                    // imu data
	};

  struct ElementPOS {
    float px, py, pz;                       // position
    // float vx, vy, vz;	                  // veolocity
  };

  struct Element6DOF {
    int id;                                 // -1 un-available, otherwise available
		int data_type;													// result type: i.e. 0/1/2/3 invalid/imu_3dof/vision/fusion
		unsigned int timestamp;                 // timestamp, in second
		unsigned int timestamp_nsec;            // timestamp, in nanosecond
    double confidence;                      // the best confidence threshold to filter the 6dof is 0.42, a recommend range is 0.35-0.55 
		double marker_distance;									// this is the distance from vpu to the single cards. > 0.0 is valid
    int sub_ids[NUM_RETURN_IDS];            // -1 un-available, otherwise available, markers' id in controller or map is the available sub_ids's index
    ElementOrientation orientation;         // orientation info
    ElementPOS pos;                         // position info
		ElementVector3 velocity;								// velocity
		ElementVector3 acceleration;						// acceleration
    ElementOrientation sub_orientation[NUM_RETURN_IDS]; //sub marker's orientation
    ElementPOS sub_pos[NUM_RETURN_IDS];                 //sub marker's position
  };

  struct Frame6DOF {
    // unsigned int timestamp;                 // timestamp, in second
    // unsigned int timestamp_nsec;            // timestamp, in nanosecond
    Element6DOF markers[NUM_RETURN_IDS];    // all marker 6-dof result

		FrameIMU imu_orientation;                    // head native imu
    ButtonState button_state;               // button state
  };

	// head pose
	struct FrameHead6DOF {
		bool data_valid;
		unsigned int sec;												// timestamp, sec
		unsigned int nsec;											// timestamp, nsec		
		ElementOrientation orientation;         // orientation
		ElementPOS position;                    // position
	};

	// this structure is used for fusion 6dof result
  struct FrameFusion6DOF {
    unsigned int sec;												// timestamp, sec
    unsigned int nsec;											// timestamp, nsec
    int id;                                 // device id
    int data_type;                          // fusion result type: i.e. 0/2/3/4 invalid/vision/predition/fake
    ElementOrientation orientation;         // orientation
    ElementPOS position;                    // position
		ElementVector3 velocity;								// velocity
		// used for rotation preparation
		OmegaMat4 omega_mat_cur;								
		OmegaMat4 omega_mat_pre;
		OmegaMat4 omega_mat_mean;
  };

  enum CalibrationState {
    CALIBRARION_RUNNING,
    CALIBRARION_SUCCEED,
    CALIBRARION_FAILED
  }; 

  struct CalibrationFrame {
    unsigned int timestamp;                 // timestamp, in second
    unsigned int timestamp_nsec;            // timestamp, in nanosecond
    Element6DOF markers[NUM_RETURN_IDS];    // all marker 6-dof result
    int superposition;                      // marker superposition state, 0 unsuperposition, 1, 2 superposition
    CalibrationState calib_state;           // calibration state
    int iteration_round;                    // 0 un-available, otherwise 1~5    
  };
  struct MarkerParams                       // Marker params
  {
    char *ptr;
    int size;
  };
  enum MarkerMode {                         // marker mode
    CARD = 1,
    CONTROLLER = 2,
    MAP = 3,
  };
  struct MarkerElement {                    // each marker element
    MarkerMode mode;                        // marker mode
    int id;                                 // marker id
    float size;                             // marker size
  };
  struct MarkerGrounp{
    MarkerMode mode;                        // marker mode
    int resturn_id;                         // marker returned id
    float size;                             // marker size
    int num_valid_sub_markers;              // num of valid sub markers in current group, <= MAX_NUM_SUB_MARKERS_IN_GROUP
    int id_sub_markers[NUM_RETURN_IDS];			// ids of sub-markers, first num_valid_sub_markers are valid, -1 if not valid
  };
  enum MarkerSetupType {
    CARD_SINGLE,                            // type to setup sigle cards, for MarkerElement
    CARD_GROUP                              // type to setup group cards, for MarkerGrounp
  };
  
  /*** algorithm base class ***/
  class AlgBase {
  public:
    class ICallback {
    public:
      virtual void OnAlgUpdateEvent(char *buffer, int offset, int size) = 0;
    };

    class ILogger {
    public:
      virtual void ALog(enum LOGLevel level, const char *msg, ...) = 0;
    };
  };
  // typedef int(*FunGetCalibParam)(void *camera_params_ptr, int cam_id, int base_id);
};