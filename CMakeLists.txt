cmake_minimum_required(VERSION 3.10)
project(fpgabuf_to_img)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找OpenCV
find_package(OpenCV REQUIRED)

# 查找Eigen3
find_package(Eigen3 REQUIRED)

# 包含目录
include_directories(${OpenCV_INCLUDE_DIRS})
include_directories(${EIGEN3_INCLUDE_DIR})
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

# 添加fpgaUtil库的源文件
set(FPGA_UTIL_SOURCES
    fpgaUtil/fpga_util.cpp
)

# 创建fpgaUtil静态库
add_library(fpgaUtil STATIC ${FPGA_UTIL_SOURCES})
target_link_libraries(fpgaUtil ${OpenCV_LIBS})
target_include_directories(fpgaUtil PUBLIC fpgaUtil)

# 主程序 (需要OpenCV)
if(OpenCV_FOUND)
    add_executable(fpgabuf_to_img main.cpp)
    target_link_libraries(fpgabuf_to_img fpgaUtil ${OpenCV_LIBS})

    # 设置输出目录
    set_target_properties(fpgabuf_to_img PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    )

    message(STATUS "Building fpgabuf_to_img with OpenCV support")
else()
    message(WARNING "OpenCV not found, skipping fpgabuf_to_img")
endif()

# 简化版本程序 (不需要OpenCV)
add_executable(simple_converter simple_converter.cpp)
target_link_libraries(simple_converter fpgaUtil)

# 设置输出目录
set_target_properties(simple_converter PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 如果在Windows上，可能需要额外的链接库
if(WIN32)
    if(TARGET fpgabuf_to_img)
        target_link_libraries(fpgabuf_to_img ws2_32)
    endif()
    target_link_libraries(simple_converter ws2_32)
endif()

message(STATUS "Building simple_converter (no OpenCV dependency)")

# 复制demo文件到构建目录
file(COPY ${CMAKE_SOURCE_DIR}/demo DESTINATION ${CMAKE_BINARY_DIR})

# 打印一些有用的信息
message(STATUS "OpenCV version: ${OpenCV_VERSION}")
message(STATUS "OpenCV include dirs: ${OpenCV_INCLUDE_DIRS}")
message(STATUS "OpenCV libraries: ${OpenCV_LIBS}")
