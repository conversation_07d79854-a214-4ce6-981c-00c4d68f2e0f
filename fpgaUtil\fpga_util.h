#pragma once
#include "api_struct_define.h"
#include "cv_define.h"
#include <vector>
#ifdef HAVE_ANDROID_OS
#include <time.h>
#endif


using namespace std;
namespace fpga_util{
    typedef LED_ALG_NAMESPACE::LOGLevel LOGLevel;
    typedef LED_ALG_NAMESPACE::uchar uchar;
    
    typedef struct FramePointsXYR{
        vector<double> xs, ys, rs, gs;
        vector<int> ids;
        double t_frame;
        unsigned int timestamp, timestamp_nsec;
        int blob_num, blob_num_ori;
        int camera_id = -1;
    }FramePointsXYR;

    static uint8_t ClcCheckSum(uint8_t *DataPtr, uint16_t aDataLen, uint8_t *checksum);
    unsigned char CheckOrCalcSum(unsigned char* apBuf, unsigned char alen, unsigned char* apSum, unsigned char aType);
    bool ReadFPGABuffer(char *buffer, int offset, int size, LED_ALG_NAMESPACE::MarkerBlobFrame &marker_blob_frame_local, LED_ALG_NAMESPACE::AlgBase::ILogger *logger = NULL);
    double MarkerBlobFrameBuffer2XYR(char* buffer, int size, FramePointsXYR &frame_points_xyr, vector<bool> flags);
    double MarkerBlobFrameBuffer2XYR(char* buffer, int size, FramePointsXYR &frame_points_xyr, int camera_id = 0, LED_ALG_NAMESPACE::AlgBase::ILogger *logger_ = NULL, bool extra_time_header = false, bool is_check_overlap = false, double stride = 1.0);
    void MarkerBlobChecker(LED_ALG_NAMESPACE::MarkerBlobFrame marker_blob_frame);
    double MarkerBlobFrame2XYR(LED_ALG_NAMESPACE::MarkerBlobFrame marker_blob_frame, FramePointsXYR &frame_points_xyr, int camera_id = 0, LED_ALG_NAMESPACE::AlgBase::ILogger *logger_ = NULL, double stride = 1.0, bool is_check_overlap = false);
    int MarkerBlobVector2XYR(vector<LED_ALG_NAMESPACE::MarkerBlob> marker_blobs, FramePointsXYR &frame_points_xyr, int camera_id = -1, bool reset_timestamp = true);
    void GetBlobAttri(LED_ALG_NAMESPACE::MarkerBlob marker_blob, double &x, double &y, double &r, double &val, double &ratio);
    int MarkerBlobArray2XYR(LED_ALG_NAMESPACE::MarkerBlob *marker_blobs, int blob_num, FramePointsXYR &frame_points_xyr, vector<bool> flags);
    int MarkerBlobArray2XYR(LED_ALG_NAMESPACE::MarkerBlob *marker_blobs, int blob_num, FramePointsXYR &frame_points_xyr, int camera_id = 0, bool reset_timestamp = true, bool is_check_overlap = false, LED_ALG_NAMESPACE::AlgBase::ILogger *logger_ = NULL, double stride = 1.0);
    int RemoveOverlappedBlobs(FramePointsXYR &frame_points_xyr, LED_ALG_NAMESPACE::AlgBase::ILogger *logger_ =NULL);
};