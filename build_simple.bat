@echo off
echo Building simple_converter (no OpenCV dependency)...

REM 检查编译器
where g++ >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: g++ compiler not found!
    echo Please install MinGW-w64 or Visual Studio with C++ support
    echo Or use build.bat for CMake-based build
    pause
    exit /b 1
)

echo Using g++ compiler...

REM 编译fpga_util.cpp
echo Compiling fpga_util.cpp...
g++ -std=c++17 -c fpgaUtil/fpga_util.cpp -o fpga_util.o -I.
if %errorlevel% neq 0 (
    echo Error compiling fpga_util.cpp
    pause
    exit /b 1
)

REM 编译simple_converter.cpp
echo Compiling simple_converter.cpp...
g++ -std=c++17 -c simple_converter.cpp -o simple_converter.o -I.
if %errorlevel% neq 0 (
    echo Error compiling simple_converter.cpp
    pause
    exit /b 1
)

REM 链接生成可执行文件
echo Linking...
g++ -std=c++17 fpga_util.o simple_converter.o -o simple_converter.exe
if %errorlevel% neq 0 (
    echo Error linking
    pause
    exit /b 1
)

REM 清理临时文件
del fpga_util.o simple_converter.o 2>nul

echo Build successful!
echo Executable: simple_converter.exe
echo.
echo Usage: simple_converter.exe demo/FpgaBuf_0.txt [output_folder]
echo This version generates SVG files instead of PNG images.
pause
