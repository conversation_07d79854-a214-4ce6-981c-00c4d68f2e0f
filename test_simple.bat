@echo off
echo Testing simple_converter...

REM 检查可执行文件是否存在
if exist "simple_converter.exe" (
    set EXECUTABLE=simple_converter.exe
) else if exist "build\bin\simple_converter.exe" (
    set EXECUTABLE=build\bin\simple_converter.exe
) else if exist "build\simple_converter.exe" (
    set EXECUTABLE=build\simple_converter.exe
) else (
    echo Error: simple_converter.exe not found!
    echo Please build the project first using build_simple.bat or build.bat
    pause
    exit /b 1
)

echo Found executable: %EXECUTABLE%

REM 检查demo文件是否存在
if not exist "demo\FpgaBuf_0.txt" (
    echo Error: demo\FpgaBuf_0.txt not found!
    echo Please make sure the demo folder exists with sample files.
    pause
    exit /b 1
)

echo Testing with demo\FpgaBuf_0.txt...

REM 运行程序
%EXECUTABLE% demo\FpgaBuf_0.txt test_output_svg

REM 检查是否成功
if %errorlevel% neq 0 (
    echo Test failed!
    pause
    exit /b 1
)

echo Test completed successfully!
echo Check the test_output_svg folder for generated SVG images.

REM 显示生成的文件数量
if exist "test_output_svg" (
    dir /b test_output_svg\*.svg > temp_count.txt 2>nul
    for /f %%i in ('type temp_count.txt ^| find /c /v ""') do set count=%%i
    del temp_count.txt 2>nul
    echo Generated %count% SVG images.
    
    REM 检查是否生成了HTML报告
    if exist "test_output_svg\report.html" (
        echo HTML report generated: test_output_svg\report.html
        echo You can open this file in a web browser to view all images.
    )
) else (
    echo Warning: test_output_svg folder not found!
)

pause
