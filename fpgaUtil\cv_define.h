/*
 *  base/cv_define.h
 *
 * Copyright (C) 2021 Ximmerse. All rights reserved.
 */

/*
 * cv define of algorithm
 *
 * Library: algorithm
 * Package: base
 * Module : defines
 * Author : Yongtao Hu
 * Modified: December 2021 By Guoxing Yu
 */

#ifndef XIM_DEFINE_H
#define XIM_DEFINE_H

#include <stdio.h>
#include <math.h>
#include <string>
#include <vector>
#include <map>
#include <set>
#include <list>
#include <deque>
#include <utility>
#include <iostream>
#include <fstream>
#include <sstream>
#include "opencv2/eigen3/Eigen/Dense"
// #include "alg_factory.h"
#include "api_struct_define.h"

#define XIM_ALG_SDK_VERSION 0x001D  //Normal, DSP support

#define XIM_ALG_SDK_VERSION_PREVIEW "_Preview"

#ifndef XIM_ALG_SDK_VERSION_PREVIEW
#define XIM_ALG_SDK_VERSION_PREVIEW ""
#define BUILD_INFO __DATE__
#endif

#define MARKER_ID_CTRL_L 92
#define MARKER_ID_CTRL_R 93
#define MARKER_ID_HEADSET_PROTOTYPE 101
#define MARKER_ID_HEADSET_POC 102
#define MARKER_ID_IDENTITY_L 110
#define MARKER_ID_IDENTITY_R 111
#define MARKER_ID_WATCH_L 122
#define MARKER_ID_WATCH_R 123

#ifndef BUILD_INFO
#define BUILD_INFO  "_" __DATE__ "_" __TIME__ 
#endif

const std::string build_time_str(BUILD_INFO);

namespace LED_ALG_NAMESPACE {
#define FPGA_BOUNDINGBOX_SIZE           25
#define PACKET_SUM_CALC                 0x00
#define PACKET_SUM_CHECK                0x01
#define PACKET_OPERATE_SUCCESS          0x00
#define PACKET_OPERATE_FAIL             0x01

	struct Point3f {
		Point3f()
			: x(0.0f),
			y(0.0f),
			z(0.0f) {}
		Point3f(float _x, float _y, float _z)
			: x(_x),
			y(_y),
			z(_z) {}
		Point3f(const Point3f& pt) {
			this->x = pt.x;
			this->y = pt.y;
			this->z = pt.z;
		}
		// operators for Point3f
		Point3f& operator += (const Point3f& a) {
			this->x += a.x;
			this->y += a.y;
			this->z += a.z;
			return *this;
		}
		Point3f& operator -= (const Point3f& a) {
			this->x -= a.x;
			this->y -= a.y;
			this->z -= a.z;
			return *this;
		}
		Point3f operator-() const {
			return Point3f(-x, -y, -z);
		}
		Point3f operator / (const float& a) {
			Point3f temp;
			temp.x = this->x / a;
			temp.y = this->y / a;
			temp.z = this->z / a;
			return temp;
		}
		Point3f operator - (const Point3f& a) {
			Point3f temp;
			temp.x = this->x - a.x;
			temp.y = this->y - a.y;
			temp.z = this->z - a.z;
			return temp;
		}
		Point3f operator + (const Point3f& a) {
			Point3f temp;
			temp.x = this->x + a.x;
			temp.y = this->y + a.y;
			temp.z = this->z + a.z;
			return temp;
		}
		Point3f operator * (const float& a) {
			Point3f temp;
			temp.x = this->x * a;
			temp.y = this->y * a;
			temp.z = this->z * a;
			return temp;
		}

		float norm() {
			float n = std::sqrt(this->x * this->x + this->y * this->y + this->z * this->z);
			return n;
		}

		void normalized() {
			float n = norm();
			if (n > 0) {
				this->x /= n;
				this->y /= n;
				this->z /= n;
			}
		}

		float x;
		float y;
		float z;
	};

	struct Point2f {
		Point2f()
			: x(0.0f),
			y(0.0f) {}
		Point2f(float _x, float _y)
			: x(_x),
			y(_y) {}
		Point2f(const Point2f& pt) {
			this->x = pt.x;
			this->y = pt.y;
		}
		// operators for Point3f
		Point2f& operator += (const Point2f& a) {
			this->x += a.x;
			this->y += a.y;
			return *this;
		}
		Point2f& operator -= (const Point2f& a) {
			this->x -= a.x;
			this->y -= a.y;
			return *this;
		}
		Point2f operator-() const {
			return Point2f(-x, -y);
		}
		Point2f operator / (const float& a) {
			Point2f temp;
			temp.x = this->x / a;
			temp.y = this->y / a;
			return temp;
		}
		Point2f operator - (const Point2f& a) {
			Point2f temp;
			temp.x = this->x - a.x;
			temp.y = this->y - a.y;
			return temp;
		}
		Point2f operator + (const Point2f& a) {
			Point2f temp;
			temp.x = this->x + a.x;
			temp.y = this->y + a.y;
			return temp;
		}
		Point2f operator * (const float& a) {
			Point2f temp;
			temp.x = this->x * a;
			temp.y = this->y * a;
			return temp;
		}
		float norm() {
			float n = std::sqrt(this->x * this->x + this->y * this->y);
			return n;
		}

		void normalized() {
			float n = norm();
			if (n > 0) {
				this->x /= n;
				this->y /= n;
			}
		}

		float x;
		float y;
	};

	struct Point3d {
		Point3d()
			: x(0.0),
			y(0.0),
			z(0.0) {}
		Point3d(double _x, double _y, double _z)
			: x(_x),
			y(_y),
			z(_z) {}
		Point3d(const Point3d& pt) {
			this->x = pt.x;
			this->y = pt.y;
			this->z = pt.z;
		}
		// operators for Point3f
		Point3d& operator += (const Point3d& a) {
			this->x += a.x;
			this->y += a.y;
			this->z += a.z;
			return *this;
		}
		Point3d& operator -= (const Point3d& a) {
			this->x -= a.x;
			this->y -= a.y;
			this->z -= a.z;
			return *this;
		}
		Point3d& operator *= (const double &a)
		{
			this->x *= a;
			this->y *= a;
			this->z *= a;
			return *this;
		}
		Point3d& operator /= (const double &a)
		{
			this->x /= a;
			this->y /= a;
			this->z /= a;
			return *this;
		}
		Point3d operator-() const {
			return Point3d(-x, -y, -z);
		}
		Point3d operator / (const float& a) {
			Point3d temp;
			temp.x = this->x / a;
			temp.y = this->y / a;
			temp.z = this->z / a;
			return temp;
		}
		Point3d operator - (const Point3d& a) {
			Point3d temp;
			temp.x = this->x - a.x;
			temp.y = this->y - a.y;
			temp.z = this->z - a.z;
			return temp;
		}
		Point3d operator + (const Point3d& a) {
			Point3d temp;
			temp.x = this->x + a.x;
			temp.y = this->y + a.y;
			temp.z = this->z + a.z;
			return temp;
		}
		Point3d operator * (const double& a) {
			Point3d temp;
			temp.x = this->x * a;
			temp.y = this->y * a;
			temp.z = this->z * a;
			return temp;
		}
		double norm() {
			double n = std::sqrt(this->x * this->x + this->y * this->y + this->z * this->z);
			return n;
		}

		void normalized() {
			double n = norm();
			if (n > 0) {
				this->x /= n;
				this->y /= n;
				this->z /= n;
			}
		}

		double x;
		double y;
		double z;
	};

	struct Point2d {
		Point2d()
			: x(0.0),
			y(0.0) {}
		Point2d(double _x, double _y)
			: x(_x),
			y(_y) {}
		Point2d(const Point2d& pt) {
			this->x = pt.x;
			this->y = pt.y;
		}
		// operators for Point3f
		Point2d& operator += (const Point2d& a) {
			this->x += a.x;
			this->y += a.y;
			return *this;
		}
		Point2d& operator -= (const Point2d& a) {
			this->x -= a.x;
			this->y -= a.y;
			return *this;
		}
		Point2d operator-() const {
			return Point2d(-x, -y);
		}
		Point2d operator / (const double& a) {
			Point2d temp;
			temp.x = this->x / a;
			temp.y = this->y / a;
			return temp;
		}
		Point2d operator - (const Point2d& a) {
			Point2d temp;
			temp.x = this->x - a.x;
			temp.y = this->y - a.y;
			return temp;
		}
		Point2d operator + (const Point2d& a) {
			Point2d temp;
			temp.x = this->x + a.x;
			temp.y = this->y + a.y;
			return temp;
		}
		Point2d operator * (const double& a) {
			Point2d temp;
			temp.x = this->x * a;
			temp.y = this->y * a;
			return temp;
		}
		double norm() {
			double n = std::sqrt(this->x * this->x + this->y * this->y);
			return n;
		}

		void normalized() {
			double n = norm();
			if (n > 0) {
				this->x /= n;
				this->y /= n;
			}
		}

		double x;
		double y;
	};

	// marker calib params
	struct RingMarkerParam {
		int id;                         // controller id
		int num;                        // actual total num of marker points, different for left/right controllers
		double model_3d[3][100];        // calibrated model points, only the first [3][num] are meaningful
	};

	// marker-imu extrinsic calib params
	struct RingMarkerIMUParam {
		double ts_shift;                // time shift (marker relative to IMU), unit s
		double tran_mat[4][4];          // tranformation matrix (marker relative to IMU) 
	};

	struct RingMarkerAndRingMarkerIMUParam {
		RingMarkerParam markerParam;
		RingMarkerIMUParam markerImuParam;
	};

	struct FPGABoundingBox
	{
		unsigned char     mHead1;
		unsigned char     mHead2;
		unsigned char     mID[3u];
		unsigned char     mUmaxUmin[3u];
		unsigned char     mVmaxVmin[3u];
		unsigned char     mArea[2u];
		unsigned char     mGreySum[3u];
		unsigned char     mXGreySum[4u];
		unsigned char     mYGreySum[4u];
		unsigned char     mCheckSum;
	};

	struct MarkerBlob {
		typedef std::vector<MarkerBlob> MarkerBlobVec;

		MarkerBlob()
			: root_id(-1),
			x_min(-1), x_max(-1), y_min(-1), y_max(-1),
			sum_gray(0), sum_gray_x(0), sum_gray_y(0),
			area(0)
		{}

		int root_id;                                  // direct parent id
		int label_id;                                 // self id
		bool color_flag;                              // black (false) or white (true) blob
		int x_min, x_max, y_min, y_max;
		double sum_gray, sum_gray_x, sum_gray_y;
		double area;
		int camera_id;
	};

	struct MarkerBlobFrame                  // struct for MarkerBlob
	{
		unsigned int timestamp;								// camera timestamp sec part, 
		unsigned int timestamp_nsec;          // camera timestamp nsec part
		unsigned int blob_num;                // actual size of marker_blobs, i.e. bounding boxes
		LED_ALG_NAMESPACE::MarkerBlob *marker_blobs;  // bounding boxes list
	};

	struct MarkerBlobFrameNoArray                  // struct for MarkerBlob
	{
		unsigned int timestamp;								// camera timestamp sec part, 
		unsigned int timestamp_nsec;          // camera timestamp nsec part
		unsigned int blob_num;                // actual size of marker_blobs, i.e. bounding boxes
	};

	// Original camera param struct VPU-based R|T
	struct CalibParam {
		double intrinsic[3][3];// intrinsic
#ifdef CAM_PARAM_FISHEYE
		double distort[4];// fisheye distort
#else
		double distort[8];// distort
#endif
	};

	// Temporary camera param struct, IMU-based R|T
	// Tested on RhynoX Pro VPU, VIO_L, VIO_R
	struct TrackingCameraCalibParams {
		int camera_id;			// vpu=0, vio_L/vio_R = 1,2
		bool fisheye;

		// intrinsic
		double intrinsic[3][3];
		double distort[8] = {0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0};		// distort - the last 4 params are 0 in fisheye mode

		// extrinsic
		double rot_vec[3] = {0.0,0.0,0.0};		// rotation vector in right hand coordinate x-right, y-down, z-forward
		double trans_vec[3] = {0.0,0.0,0.0};	// translation vector in right hand coordinate x-right, y-down, z-forward

		TrackingCameraCalibParams() : camera_id(-1), fisheye(false) {};
	};

	
	enum CamID {
		VIO_LEFT = 1,       // left VIO
		VIO_RIGHT = 2,      // right VIO
		VPU_FORWARD = 0,    // forward VPU
		VPU_UPWARD = 3,     // upward VPU
		RGB = 4,            // RGB
		TOF = 5,            // ToF
		IMU = 100,            // IMU
		Eye = 200,            // Eye center
	};

	enum CamDistModel {
		FISHEYE = 0,        // fisheye distortion model, w/ 4 params [k1,k2,k3,k4]
		RADIAL = 1,         // radial+tangential distortion model, w/ 8 params [k1,k2,p1,p2,k3,k4,k5,k6]
	};

	// Used by RhynoX 2.0, compatible to RhynoX Pro
	// When the device is not a camera, model < 0.
	struct CamCalibParam
	{
		int id;                 // camera id
		int base_id;            // camera base id
		int size[2];              // img size [width, height]
		int model;       // distortion model

		// intrinsic param
		double intrinsic[3][3];   // intrinsic
		double distort[8];        // distort

		// extrinsic param
		double rot_mat[3][3];     // rotation matrix
		double trans[3];          // translation vector

		CamCalibParam() :
			id(int(-1)),
			base_id(int(-1)),
			size{ 0,0 },
			model(int(-1)),
			intrinsic{ {0,0,0},{0,0,0},{0,0,0} },
			distort{ 0,0,0,0,0,0,0,0 },
			rot_mat{ {1,0,0},{0,1,0},{0,0,1} },
			trans{ 0,0,0 } { }
	};

	struct VPUPoseFrame {
		bool data_valid;
		unsigned int sec;
		unsigned int nsec;
		double timestamp;
		Eigen::Quaterniond orientation;
		Eigen::Vector3d position;
		VPUPoseFrame() : data_valid(false), sec(0), nsec(0), timestamp(0.0), orientation(Eigen::Quaterniond(1.0, 0.0, 0.0, 0.0)), position(Eigen::Vector3d(0.0, 0.0, 0.0)) {};
	};

	struct MarkerElement_ {                   // each marker element
		int sn;
		int id;                                 // marker id
		double size;                            // marker size
	};
	struct MarkerGrounp_ {
		int sn;
		MarkerMode mode;                        // marker mode
		int resturn_id;                         // marker returned id
		std::vector<double> size;               // marker size
		std::vector<int> valid_ids_sub_markers; // 
	};

#ifndef CV_PI
#define CV_PI 3.1415926535897932384626433832795
#endif
	typedef unsigned char uchar;
	typedef std::vector< char > CharVec;
	typedef std::vector< float > FloatVec;
	typedef std::vector< int > IntVec;
	typedef std::vector< uchar > UcharVec;
	typedef std::vector< void* > VoidPtrVec;

	typedef std::vector< FloatVec > FloatVec2;
	typedef std::vector< UcharVec > UcharVec2;
	typedef std::vector< LED_ALG_NAMESPACE::Point2f > Point2fVec;
	typedef std::vector< LED_ALG_NAMESPACE::Point3f > Point3fVec;
	typedef std::vector< LED_ALG_NAMESPACE::Point2d > Point2dVec;
	typedef std::vector< LED_ALG_NAMESPACE::Point3d > Point3dVec;
	typedef std::deque< LED_ALG_NAMESPACE::Point3d> Point3dDeque;
	typedef std::vector< Point3dDeque > Point3DDequeVec2;
	typedef std::vector< Point3dVec > Point3dVec2;

	typedef std::vector< std::pair<char*, int> > BlobIntPairVec;
	typedef std::vector< std::pair<int, int> > IntIntPairVec;
	typedef std::vector< std::pair<int, float> > IntFloatPairVec;
	typedef std::vector< IntFloatPairVec > IntFloatPairVec2;

#ifndef my_max
#define my_max(a,b)            (((a) > (b)) ? (a) : (b))
#endif
#ifndef my_min
#define my_min(a,b)            (((a) < (b)) ? (a) : (b))
#endif

};// XIM_DEFINE_H

namespace JsonIO {
	struct CardSingle {
		char calib_file[256];
		int marker_ids[LED_ALG_NAMESPACE::NUM_MARKERS_ALL];
		double marker_size[LED_ALG_NAMESPACE::NUM_MARKERS_ALL];
		int num_valid_markers;
	};

	struct CardGroup {
		char calib_file[256];
		char mode_type[256];
		int group_id;
		int marker_ids[LED_ALG_NAMESPACE::MAX_NUM_SUB_MARKERS_IN_GROUP];
		double marker_size[LED_ALG_NAMESPACE::MAX_NUM_SUB_MARKERS_IN_GROUP];
		int num_valid_markers;
	};

	struct CardDefine {
		bool is_single;
		CardSingle card_single;
		CardGroup card_group;
	};
};

namespace marker_fusion_comm {
	struct IMUCalibPrams {
		Eigen::Matrix3d imu_acc_scales;
		Eigen::Vector3d imu_acc_bias;
		double imu_acc_noise;
		Eigen::Matrix3d imu_gyro_scales;
		Eigen::Vector3d imu_gyro_bias;
		double imu_gyro_noise;
		bool fixed_bias;
		double max_acc_norm;
		double max_dt;
	};
	class FusionConfigParams {
	public:
		FusionConfigParams() : scale(1.0), xim_p_noise(0.02), xim_q_noise(0.04), vio_p_noise(0.015), vio_q_noise(0.025), vio_time_ratio_params(0.015), xim_time_ratio_params(0.015), xim_delay(0.0), vio_delay(0.0), tag_data_seq_size(1), vio_data_seq_size(1), imu_data_seq_size(1) {
			max_delta_t = 0.05;
			max_acc_norm = 50.0;
			xim_p_ic.setZero();
			vio_p_ic.setZero();
			xim_p_wv.setZero();
			xim_q_wv.setIdentity();
			xim_q_ic.setIdentity();
			vio_q_ic.setIdentity();
			xim_pose_enable = true;
			xim_pose_fixed_scale = true;
			xim_pose_fixed_p_ic = true;
			xim_pose_fixed_q_ic = true;
			xim_pose_fixed_p_wv = true;
			xim_pose_fixed_q_wv = true;
			vio_pose_enable = true;
			vio_pose_fixed_scale = true;
			vio_pose_fixed_p_ic = true;
			vio_pose_fixed_q_ic = true;
			vio_pose_fixed_p_wv = true;
			vio_pose_fixed_q_wv = true;
			xim_provides_absolute_measurements = true;
			xim_measurement_world_sensor = true;
			xim_use_fixed_covariance = true;
			xim_pose_measurement_minimum_dt = 0.01;
			xim_enable_mah_outlier_rejection = false;
			xim_mah_threshold = 100.0;
			vio_provides_absolute_measurements = true;
			vio_measurement_world_sensor = true;
			vio_use_fixed_covariance = true;
			vio_pose_measurement_minimum_dt = 0.001;
			vio_enable_mah_outlier_rejection = false;
			vio_mah_threshold = 100.0;
			imu_keep_tracking_count = 20;
		};
		~FusionConfigParams() {};
		Eigen::Vector3d xim_p_ic, vio_p_ic, xim_p_wv;
		Eigen::Quaterniond xim_q_wv, xim_q_ic, vio_q_ic;
		double scale;
		double xim_p_noise, xim_v_noise, xim_q_noise, vio_p_noise, vio_q_noise;
		double vio_time_ratio_params, xim_time_ratio_params;
		double xim_delay, vio_delay;
		double max_delta_t, max_acc_norm;
		int tag_data_seq_size, vio_data_seq_size, imu_data_seq_size;
		bool xim_pose_enable, xim_pose_fixed_scale, xim_pose_fixed_p_ic, xim_pose_fixed_q_ic, xim_pose_fixed_p_wv, xim_pose_fixed_q_wv, vio_pose_enable, vio_pose_fixed_scale, vio_pose_fixed_p_ic, vio_pose_fixed_q_ic, vio_pose_fixed_p_wv, vio_pose_fixed_q_wv;
		bool xim_provides_absolute_measurements, vio_provides_absolute_measurements;
		bool xim_measurement_world_sensor, vio_measurement_world_sensor;
		bool xim_use_fixed_covariance, vio_use_fixed_covariance;
		double xim_pose_measurement_minimum_dt, vio_pose_measurement_minimum_dt;
		bool xim_enable_mah_outlier_rejection, vio_enable_mah_outlier_rejection;
		double xim_mah_threshold, vio_mah_threshold;
		int imu_keep_tracking_count;
	};
}

#endif 