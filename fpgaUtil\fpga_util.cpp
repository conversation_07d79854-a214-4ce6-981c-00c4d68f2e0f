#include "fpga_util.h"
#include <cstring>
#include <cstdlib>
#include <cstdio>
#include <cstdint>
// #include <src/logging.h>  // 注释掉不存在的头文件

// 定义LOGE宏用于错误日志
#define LOGE(fmt, ...) printf("[ERROR] " fmt "\n", ##__VA_ARGS__)
namespace fpga_util{

uint8_t ClcCheckSum(uint8_t *DataPtr, uint16_t aDataLen, uint8_t *checksum)
{
	uint8_t ret = PACKET_OPERATE_SUCCESS;
	uint8_t aSum = 0;
	uint8_t temp = 0;
	uint16_t i = 0;

	for (i = 0; i<aDataLen; i++) {
		temp = (DataPtr[i] >> 4u);
		aSum += temp;
		temp = (DataPtr[i] & 0x0F);
		aSum += temp;
	}
	temp = (checksum[0] >> 4u);
	temp = temp + aSum;
	aSum = (aSum & 0xF0) + (temp & 0x0F);
	if (aSum != checksum[0]) {
		checksum[0] = aSum;
		ret = PACKET_OPERATE_FAIL;
	}
	return ret;
}

unsigned char CheckOrCalcSum(unsigned char* apBuf, unsigned char alen, unsigned char* apSum, unsigned char aType)
{
  unsigned short tmp = 0u;
  unsigned short i = 0;

  for (i = 0; i < alen; i++)
  {
    tmp += apBuf[i];
  }

  if (aType == PACKET_SUM_CALC)
  {
    apSum[1] = (unsigned char)(tmp >> 8);
    apSum[0] = (unsigned char)tmp;
  }
  else
  {
    if (apSum[1] == (unsigned char)(tmp >> 8) && apSum[0] == (unsigned char)tmp)
    {
      return PACKET_OPERATE_SUCCESS;
    }
    else
    {
      return PACKET_OPERATE_FAIL;
    }
  }
  return PACKET_OPERATE_SUCCESS;
}

bool ReadFPGABuffer(char *buffer, int offset, int size, LED_ALG_NAMESPACE::MarkerBlobFrame &marker_blob_frame_local, LED_ALG_NAMESPACE::AlgBase::ILogger *logger_) {

	if (buffer == NULL || size <= 0)
	{
#ifdef XIM_ALG_LOG
		if(logger_!=NULL)
				logger_->ALog(LOGLevel::_LOG_INFO, "tag_led_alg: fpga_data_info empty buffer, data_size, %d\n", size);
#endif
		return false;
	}
	// LED_ALG_NAMESPACE::MarkerBlobFrame marker_blob_frame_local;
	marker_blob_frame_local.timestamp = 0;
	marker_blob_frame_local.timestamp_nsec = 0;
	marker_blob_frame_local.blob_num = 0;
	marker_blob_frame_local.marker_blobs = NULL;
	memcpy(&(marker_blob_frame_local.timestamp), buffer + offset, 4);
	memcpy(&(marker_blob_frame_local.timestamp_nsec), buffer + offset + 4, 4);
	double fpga_data_timestamp = (double)marker_blob_frame_local.timestamp + (double)marker_blob_frame_local.timestamp_nsec / 1.0e9;
	char* blobs_data_buffer = buffer + 8;
	int blobs_data_buffer_size = size - 8;


#ifdef XIM_ALG_LOG
	if(logger_!=NULL)
			logger_->ALog(LOGLevel::_LOG_INFO, "tag_led_alg: fpga_data_info timestamp, %f, data_size, %d\n", fpga_data_timestamp, blobs_data_buffer_size);
#endif

	if (blobs_data_buffer_size <= 0)
	{
		return false;
	}

	marker_blob_frame_local.blob_num = blobs_data_buffer_size / FPGA_BOUNDINGBOX_SIZE;
	int marker_blob_vector_size = marker_blob_frame_local.blob_num * sizeof(LED_ALG_NAMESPACE::MarkerBlob);
	marker_blob_frame_local.marker_blobs = (LED_ALG_NAMESPACE::MarkerBlob *)malloc(marker_blob_vector_size);
	LED_ALG_NAMESPACE::FPGABoundingBox *fpga_boundingbox = (LED_ALG_NAMESPACE::FPGABoundingBox *)malloc(blobs_data_buffer_size);
	memcpy(fpga_boundingbox, blobs_data_buffer, blobs_data_buffer_size);

	unsigned char checksum[2] = { 0, 0 };
	int index = 0;
	for (unsigned int i = 0; i < marker_blob_frame_local.blob_num; i++)
	{
		if (fpga_boundingbox[i].mHead1 != 0x55 || fpga_boundingbox[i].mHead2 >> 4 != 0xa) {
			// #ifdef XIM_ALG_LOG
			// 	if (logger_ != NULL) logger_->ALog(LOGLevel::LOG_DEBUG, "tag_alg: invalid fpga_bounding_box\n");
			// #endif
			continue;
		}
		checksum[0] = fpga_boundingbox[i].mCheckSum;
		unsigned char check_ret = ClcCheckSum((unsigned char *)(&fpga_boundingbox[i]), FPGA_BOUNDINGBOX_SIZE - 1, &checksum[0]);
		if (check_ret == PACKET_OPERATE_SUCCESS)
		{
			unsigned int temp = 0;
			temp = (fpga_boundingbox[i].mID[0] & 0x000000ff) << 16;
			temp = temp | ((fpga_boundingbox[i].mID[1] & 0x000000ff) << 8);
			temp = temp | ((fpga_boundingbox[i].mID[2] & 0x000000ff) << 0);

			//color decode
			if ((temp & 0x00800000) == 0x00800000)
			{
				marker_blob_frame_local.marker_blobs[index].color_flag = true;
			}
			else
			{
				marker_blob_frame_local.marker_blobs[index].color_flag = false;
			}

			//camera id decode
			unsigned int camera_id = (temp & 0x00700000) >> 20;
			marker_blob_frame_local.marker_blobs[index].camera_id = int(camera_id);

			//label decode
			unsigned int label_id = (temp & 0x000ffc00) >> 10;
			unsigned int root_id = temp & 0x000003ff;
			marker_blob_frame_local.marker_blobs[index].root_id = int(root_id);
			marker_blob_frame_local.marker_blobs[index].label_id = int(label_id);

			//umax umin decode 
			temp = 0;
			temp = (fpga_boundingbox[i].mUmaxUmin[0] & 0x000000ff) << 16;
			temp = temp | ((fpga_boundingbox[i].mUmaxUmin[1] & 0x000000ff) << 8);
			temp = temp | ((fpga_boundingbox[i].mUmaxUmin[2] & 0x000000ff) << 0);

			unsigned int x_max = (temp & 0x00fff000) >> 12;
			unsigned int x_min = (temp & 0x00000fff) >> 0;
			marker_blob_frame_local.marker_blobs[index].x_max = (int)x_max;
			marker_blob_frame_local.marker_blobs[index].x_min = (int)x_min;

			//vmax vmin decode
			temp = 0;
			temp = (fpga_boundingbox[i].mVmaxVmin[0] & 0x000000ff) << 16;
			temp = temp | ((fpga_boundingbox[i].mVmaxVmin[1] & 0x000000ff) << 8);
			temp = temp | ((fpga_boundingbox[i].mVmaxVmin[2] & 0x000000ff) << 0);

			unsigned int y_max = (temp & 0x00fff000) >> 12;
			unsigned int y_min = (temp & 0x00000fff) >> 0;
			marker_blob_frame_local.marker_blobs[index].y_max = (int)y_max;
			marker_blob_frame_local.marker_blobs[index].y_min = (int)y_min;

			//area decode 
			temp = 0;
			temp = (fpga_boundingbox[i].mArea[0] & 0x000000ff) << 8;
			temp = temp | ((fpga_boundingbox[i].mArea[1] & 0x000000ff) << 0);

			unsigned int area = temp;
			marker_blob_frame_local.marker_blobs[index].area = (double)area;

			//grey sum decode
			temp = 0;
			temp = (fpga_boundingbox[i].mGreySum[0] & 0x000000ff) << 16;
			temp = temp | ((fpga_boundingbox[i].mGreySum[1] & 0x000000ff) << 8);
			temp = temp | ((fpga_boundingbox[i].mGreySum[2] & 0x000000ff) << 0);

			unsigned int grey_sum = temp;
			marker_blob_frame_local.marker_blobs[index].sum_gray = (double)grey_sum;

			//x-grey sum decode
			temp = 0;
			temp = (fpga_boundingbox[i].mXGreySum[0] & 0x000000ff) << 24;
			temp = temp | ((fpga_boundingbox[i].mXGreySum[1] & 0x000000ff) << 16);
			temp = temp | ((fpga_boundingbox[i].mXGreySum[2] & 0x000000ff) << 8);
			temp = temp | ((fpga_boundingbox[i].mXGreySum[3] & 0x000000ff) << 0);

			unsigned int x_grey_sum = temp;
			marker_blob_frame_local.marker_blobs[index].sum_gray_x = (double)x_grey_sum;

			//y-grey sum decode
			temp = 0;
			temp = (fpga_boundingbox[i].mYGreySum[0] & 0x000000ff) << 24;
			temp = temp | ((fpga_boundingbox[i].mYGreySum[1] & 0x000000ff) << 16);
			temp = temp | ((fpga_boundingbox[i].mYGreySum[2] & 0x000000ff) << 8);
			temp = temp | ((fpga_boundingbox[i].mYGreySum[3] & 0x000000ff) << 0);

			unsigned int y_grey_sum = temp;
			marker_blob_frame_local.marker_blobs[index].sum_gray_y = (double)y_grey_sum;
			index++;
		}
		else
		{
			 printf("the %dth blob check sum error %d != %d\n", i, (uint8_t)checksum[0], (uint8_t)fpga_boundingbox[i].mCheckSum);
#ifdef XIM_ALG_LOG
			if (logger_ != NULL)
				logger_->ALog(LOGLevel::_LOG_ERROR, "tag_led_alg: the %dth blob check sum error %d != %d\n", i, (uchar)checksum[0], (uchar)fpga_boundingbox[i].mCheckSum);
#endif
		}
	}
	marker_blob_frame_local.blob_num = index;
	free(fpga_boundingbox);
	fpga_boundingbox = NULL;

	// free(marker_blob_frame_local.marker_blobs);	
	return true;	

}

double MarkerBlobFrameBuffer2XYR(char* buffer, int size, FramePointsXYR &frame_points_xyr, vector<bool> flags){
	int offset = 0;
	// convert to MarkerBlobFrame
	LED_ALG_NAMESPACE::MarkerBlobFrame marker_blob_frame;
	memcpy((char*) &marker_blob_frame,  buffer + offset, sizeof(LED_ALG_NAMESPACE::MarkerBlobFrameNoArray));

	MarkerBlobArray2XYR((LED_ALG_NAMESPACE::MarkerBlob*)(buffer + offset + sizeof(LED_ALG_NAMESPACE::MarkerBlobFrameNoArray)), 
			marker_blob_frame.blob_num, frame_points_xyr, flags);

	frame_points_xyr.t_frame = (double) marker_blob_frame.timestamp + (double) marker_blob_frame.timestamp_nsec * 1.0e-9;
	frame_points_xyr.timestamp = marker_blob_frame.timestamp;
	frame_points_xyr.timestamp_nsec = marker_blob_frame.timestamp_nsec;
	return frame_points_xyr.t_frame;
}

double MarkerBlobFrameBuffer2XYR(char* buffer, int size,
	FramePointsXYR &frame_points_xyr, int camera_id, LED_ALG_NAMESPACE::AlgBase::ILogger *logger_, bool extra_time_header, bool is_check_overlap, double stride){
	int offset = 0;
	if(extra_time_header) offset = 8;


	// convert to MarkerBlobFrame
	LED_ALG_NAMESPACE::MarkerBlobFrame marker_blob_frame;
	memcpy((char*) &marker_blob_frame,  buffer + offset, sizeof(LED_ALG_NAMESPACE::MarkerBlobFrameNoArray));

	if (extra_time_header){
		uint64_t time_header = 0;
		memcpy((char*)&time_header, buffer, sizeof(uint64_t));
		marker_blob_frame.timestamp = (unsigned int) (time_header/1000000000);
		marker_blob_frame.timestamp_nsec = (unsigned int) (time_header%1000000000);
	}

	// if (marker_blob_frame_no_arr.blob_num > 0)
	// 	marker_blob_frame.marker_blobs = (LED_ALG_NAMESPACE::MarkerBlob *) malloc(sizeof(LED_ALG_NAMESPACE::MarkerBlob) * marker_blob_frame_no_arr.blob_num);
	// else
	// 	marker_blob_frame.marker_blobs = NULL;
	// memcpy((char*) marker_blob_frame.marker_blobs, (char*)(buffer + sizeof(LED_ALG_NAMESPACE::MarkerBlobFrameNoArray)), 
	// 	sizeof(LED_ALG_NAMESPACE::MarkerBlob) * marker_blob_frame_no_arr.blob_num);

	// double t_frame = MarkerBlobFrame2XYR(marker_blob_frame, frame_points_xyr, camera_id, logger_);
	// if (marker_blob_frame.marker_blobs != NULL) free(marker_blob_frame.marker_blobs);



	
	frame_points_xyr.t_frame = (double) marker_blob_frame.timestamp + (double) marker_blob_frame.timestamp_nsec * 1.0e-9;
	frame_points_xyr.timestamp = marker_blob_frame.timestamp;
	frame_points_xyr.timestamp_nsec = marker_blob_frame.timestamp_nsec;

	MarkerBlobArray2XYR((LED_ALG_NAMESPACE::MarkerBlob*)(buffer + offset + sizeof(LED_ALG_NAMESPACE::MarkerBlobFrameNoArray)), marker_blob_frame.blob_num, frame_points_xyr, camera_id, false, is_check_overlap, logger_, stride);
	
	#ifdef HAVE_ANDROID_OS
	#ifdef XIM_ALG_LOG
		if(logger_!=NULL){
				struct timespec current_t;
			    clock_gettime(CLOCK_BOOTTIME, &current_t);
    			double current_time_sec = (double)current_t.tv_sec + (double)current_t.tv_nsec * 1.0e-9;	 // convert time to second
				logger_->ALog(LOGLevel::LOG_INFO, "tag_led_alg: marker_blob_data_info timestamp, %f, latency %.2f ms, camera_id = %d, size = %d, blob_num = %d/%d\n", 
				frame_points_xyr.t_frame, (current_time_sec-frame_points_xyr.t_frame) *1000,
				camera_id, size, frame_points_xyr.blob_num, marker_blob_frame.blob_num);
		}
	#endif
	#endif
	// get XYR
	return frame_points_xyr.t_frame;
}

void MarkerBlobChecker(LED_ALG_NAMESPACE::MarkerBlobFrame marker_blob_frame)
{
	size_t blob_num = marker_blob_frame.blob_num;
	LED_ALG_NAMESPACE::MarkerBlob *blobs = marker_blob_frame.marker_blobs;

	for (size_t i = 0; i < blob_num; i++)
	{
		LED_ALG_NAMESPACE::MarkerBlob blob = blobs[i];

		double center_x = blob.sum_gray_x / blob.sum_gray;
		double center_y = blob.sum_gray_y / blob.sum_gray;

		if (blob.x_max < blob.x_min || blob.y_max < blob.y_min)
		{
			LOGE("Blob check fail. %d %d %d %d %d %d %d %d", i, blob.root_id, blob.label_id, blob.color_flag, blob.x_min, blob.x_max, blob.y_min, blob.y_max);
		}
		if ((int)center_x < blob.x_min || (int)center_x > blob.x_max || (int)center_y < blob.y_min || (int)center_y > blob.y_max)
		{
			LOGE("Blob center check fail.%d, %f, %f, %d, %d, %d, %d", i, center_x, center_y, blob.x_min, blob.x_max, blob.y_min, blob.y_max);
		}
		
	}


}

double MarkerBlobFrame2XYR(LED_ALG_NAMESPACE::MarkerBlobFrame marker_blob_frame 
	, FramePointsXYR &frame_points_xyr, int camera_id, LED_ALG_NAMESPACE::AlgBase::ILogger *logger_, double stride, bool is_check_overlap){
	bool reset_timestamp = true;
	MarkerBlobArray2XYR(marker_blob_frame.marker_blobs, marker_blob_frame.blob_num, frame_points_xyr, camera_id, reset_timestamp, is_check_overlap, logger_, stride);
	frame_points_xyr.t_frame = (double) marker_blob_frame.timestamp + (double) marker_blob_frame.timestamp_nsec * 1.0e-9;
	frame_points_xyr.timestamp = marker_blob_frame.timestamp;
	frame_points_xyr.timestamp_nsec = marker_blob_frame.timestamp_nsec;
	
	return frame_points_xyr.t_frame;
}

int MarkerBlobVector2XYR(vector<LED_ALG_NAMESPACE::MarkerBlob> marker_blobs, FramePointsXYR &frame_points_xyr, int camera_id, bool reset_timestamp){
	return MarkerBlobArray2XYR(&marker_blobs[0], marker_blobs.size(), frame_points_xyr, camera_id, reset_timestamp);
	
}

void GetBlobAttri(LED_ALG_NAMESPACE::MarkerBlob marker_blob, double &x, double &y, double &r, double &val, double &ratio){
	ratio = double(marker_blob.x_max-  marker_blob.x_min+1)
		/max(double(marker_blob.y_max-  marker_blob.y_min+1), 1.0);
	ratio = max(ratio, 1/ratio);	
	r = sqrt(marker_blob.area / CV_PI);
	val = marker_blob.sum_gray/marker_blob.area;
	x = marker_blob.sum_gray_x/marker_blob.sum_gray;
	y = marker_blob.sum_gray_y/marker_blob.sum_gray;
}

int MarkerBlobArray2XYR(LED_ALG_NAMESPACE::MarkerBlob *marker_blobs, int blob_num, FramePointsXYR &frame_points_xyr, vector<bool> flags){

	frame_points_xyr.xs.clear();
	frame_points_xyr.ys.clear();
	frame_points_xyr.rs.clear();
	frame_points_xyr.gs.clear();
	frame_points_xyr.ids.clear();

	int blob_num_ori = 0;
	for (int index = 0; index < blob_num; index++) {
		double x, y, r, val;
		double ratio;
		LED_ALG_NAMESPACE::MarkerBlob marker_blob = marker_blobs[index];
		blob_num_ori ++;

		if (!flags.empty() && !flags[index]) continue;

		GetBlobAttri(marker_blob, x, y, r, val, ratio);

		// append
		frame_points_xyr.xs.push_back(x);
		frame_points_xyr.ys.push_back(y);
		frame_points_xyr.rs.push_back(r);
		frame_points_xyr.gs.push_back(val);
		frame_points_xyr.ids.push_back(index);
	}

	frame_points_xyr.blob_num = frame_points_xyr.xs.size(); // valid num
	frame_points_xyr.blob_num_ori = blob_num_ori;
	return frame_points_xyr.blob_num;
}

int MarkerBlobArray2XYR(LED_ALG_NAMESPACE::MarkerBlob *marker_blobs, int blob_num, FramePointsXYR &frame_points_xyr, int camera_id, bool reset_timestamp, 
					bool is_check_overlap, LED_ALG_NAMESPACE::AlgBase::ILogger *logger_, double stride){
	frame_points_xyr.xs.clear();
	frame_points_xyr.ys.clear();
	frame_points_xyr.rs.clear();
	frame_points_xyr.gs.clear();
	frame_points_xyr.ids.clear();

	int blob_num_ori = 0;
	map<int, bool> parents_set;
	// for (int index = 0; index < blob_num; index++){
	// 	LED_ALG_NAMESPACE::MarkerBlob marker_blob = marker_blobs[index];
	// 	if(marker_blob.root_id > 0) parents_set[marker_blob.root_id] = true;
	// }
	//int flat_count = 0;
	for (int index = 0; index < blob_num; index++) {
		double x, y, r, val;
		double ratio;
		LED_ALG_NAMESPACE::MarkerBlob marker_blob = marker_blobs[index];
		if (camera_id >= 0 && marker_blob.camera_id != camera_id) continue;
		blob_num_ori ++;
		if (parents_set.count(marker_blob.label_id) > 0) continue; // is parent
		if (marker_blob.color_flag != 1  ||marker_blob.root_id != 0) continue;

		GetBlobAttri(marker_blob, x, y, r, val, ratio);

		// check rectangle	
		r *= stride;
		if(r > 3 && ratio>4) 
			continue; 

		// check radius
		if(r < 1) continue;
		if(r > 20) continue;

		// check intensity

		// if (val < 40) {
		// 	continue;
		// }
		//if (marker_blob.area > 20 && val < 80) {
		//	flat_count++;
		//	continue; //not gaussian shape
		//}

		// get x,y
		x *= stride;
		y *= stride;

		// check overlapped blobs
		bool is_overlapped = false;
		if (is_check_overlap){
			for (size_t ii = 0; ii < frame_points_xyr.xs.size(); ii ++){
				double dist_sq = (frame_points_xyr.xs[ii] - x) * (frame_points_xyr.xs[ii] - x)
					+ (frame_points_xyr.ys[ii] - y) * (frame_points_xyr.ys[ii] - y);	
				if(dist_sq < frame_points_xyr.rs[ii] * frame_points_xyr.rs[ii] /4.0 && dist_sq < r * r /4.0){
					// blob center inside another blob
					#ifdef XIM_ALG_LOG
					if (logger_ != NULL)
						logger_->ALog(LOGLevel::_LOG_ERROR, "tag_led_alg: blobs overlap!!! (x,y,r) = (%.2f, %.2f, %.2f), (%.2f, %.2f, %.2f)\n",
							frame_points_xyr.xs[ii], frame_points_xyr.ys[ii], frame_points_xyr.rs[ii],
							x, y, r);
					#endif

					#ifndef HAVE_ANDROID_OS
					printf("tag_led_alg: blobs overlap!!! (x,y,r) = (%.2f, %.2f, %.2f), (%.2f, %.2f, %.2f)\n", 
							frame_points_xyr.xs[ii], frame_points_xyr.ys[ii], frame_points_xyr.rs[ii],
							x, y, r);
					#endif
					is_overlapped = true;
				}
			}				
		}
		if (is_overlapped) continue;
		//double r_rel = r * marker_blob.sum_gray/ marker_blob.area /100;
		// append
		frame_points_xyr.xs.push_back(x);
		frame_points_xyr.ys.push_back(y);
		frame_points_xyr.rs.push_back(r);
		frame_points_xyr.gs.push_back(val);
		frame_points_xyr.ids.push_back(index);
		//rs.push_back(r_rel);
	}
	frame_points_xyr.blob_num = frame_points_xyr.xs.size(); // valid num
	frame_points_xyr.blob_num_ori = blob_num_ori;
	if (camera_id>=0) {
		frame_points_xyr.camera_id = camera_id;
	}
	if (reset_timestamp){
		frame_points_xyr.t_frame = 0;
		frame_points_xyr.timestamp = 0;
		frame_points_xyr.timestamp_nsec = 0;
	}

	//printf("%d\n", flat_count);
	return frame_points_xyr.blob_num;
}

int RemoveOverlappedBlobs(FramePointsXYR &frame_points_xyr, LED_ALG_NAMESPACE::AlgBase::ILogger *logger_ ){
	int count = 0;
	for (int ii = 0; ii < frame_points_xyr.blob_num  - 1; ii ++){
		for(int jj = ii+1; jj< frame_points_xyr.blob_num; jj ++){
			double dist_sq = (frame_points_xyr.xs[ii] - frame_points_xyr.xs[jj]) * (frame_points_xyr.xs[ii] - frame_points_xyr.xs[jj])
				+ (frame_points_xyr.ys[ii] - frame_points_xyr.ys[jj]) * (frame_points_xyr.ys[ii] - frame_points_xyr.ys[jj]);
			if(dist_sq < frame_points_xyr.rs[ii] * frame_points_xyr.rs[ii] || dist_sq < frame_points_xyr.rs[jj] * frame_points_xyr.rs[jj]){
				// blob center inside another blob
				#ifdef XIM_ALG_LOG
				if (logger_ != NULL)
					logger_->ALog(LOGLevel::_LOG_ERROR, "tag_led_alg: the %dth and %dth blobs overlap!!! (x,y,r) = (%.2f, %.2f, %.2f), (%.2f, %.2f, %.2f)\n",
						ii, jj,
						frame_points_xyr.xs[ii], frame_points_xyr.ys[ii], frame_points_xyr.rs[ii],
						frame_points_xyr.xs[jj], frame_points_xyr.ys[jj], frame_points_xyr.rs[jj]);
				#endif
				count ++;			
			}
			
		}
	}
	return count;
}

};