@echo off
echo Building fpgabuf_to_img...

REM 检查是否存在build目录，如果不存在则创建
if not exist build mkdir build

REM 进入build目录
cd build

REM 检查是否存在vcpkg工具链文件
set VCPKG_TOOLCHAIN=""
if exist "tools\vcpkg\scripts\buildsystems\vcpkg.cmake" (
    set VCPKG_TOOLCHAIN=-DCMAKE_TOOLCHAIN_FILE=tools/vcpkg/scripts/buildsystems/vcpkg.cmake
    echo Found vcpkg toolchain, using it...
)

REM 运行CMake配置
echo Running CMake configuration...
cmake .. -G "Visual Studio 16 2019" -A x64 %VCPKG_TOOLCHAIN%

REM 如果CMake配置失败，尝试其他生成器
if %errorlevel% neq 0 (
    echo Trying with Visual Studio 17 2022...
    cmake .. -G "Visual Studio 17 2022" -A x64 %VCPKG_TOOLCHAIN%
)

REM 如果仍然失败，尝试MinGW
if %errorlevel% neq 0 (
    echo Trying with <PERSON>GW Makefiles...
    cmake .. -G "MinGW Makefiles" %VCPKG_TOOLCHAIN%
)

REM 如果仍然失败，提示用户
if %errorlevel% neq 0 (
    echo CMake configuration failed. Please make sure:
    echo 1. CMake is installed and in PATH
    echo 2. OpenCV is installed and can be found by CMake
    echo 3. Visual Studio or MinGW is installed
    echo.
    echo To install OpenCV automatically, run: install_opencv.bat
    echo Or follow manual installation guide: install_opencv_manual.bat
    pause
    exit /b 1
)

REM 构建项目
echo Building project...
cmake --build . --config Release

REM 检查构建是否成功
if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build successful!
echo Executable location: build\bin\Release\fpgabuf_to_img.exe (or build\fpgabuf_to_img.exe)
pause
