@echo off
echo Building fpgabuf_to_img...

REM 检查是否存在build目录，如果不存在则创建
if not exist build mkdir build

REM 进入build目录
cd build

REM 运行CMake配置
echo Running CMake configuration...
cmake .. -G "Visual Studio 16 2019" -A x64

REM 如果CMake配置失败，尝试其他生成器
if %errorlevel% neq 0 (
    echo Trying with different generator...
    cmake .. -G "MinGW Makefiles"
)

REM 如果仍然失败，提示用户
if %errorlevel% neq 0 (
    echo CMake configuration failed. Please make sure:
    echo 1. CMake is installed and in PATH
    echo 2. OpenCV is installed and can be found by CMake
    echo 3. Visual Studio or MinGW is installed
    pause
    exit /b 1
)

REM 构建项目
echo Building project...
cmake --build . --config Release

REM 检查构建是否成功
if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build successful!
echo Executable location: build\bin\Release\fpgabuf_to_img.exe (or build\fpgabuf_to_img.exe)
pause
