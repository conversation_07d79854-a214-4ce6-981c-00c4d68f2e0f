@echo off
echo Installing OpenCV using vcpkg...

REM 检查是否已安装git
where git >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Git is not installed or not in PATH
    echo Please install Git from https://git-scm.com/download/win
    pause
    exit /b 1
)

REM 检查是否已安装Visual Studio或Build Tools
where cl >nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: Visual Studio C++ compiler not found in PATH
    echo You may need to run this from a Visual Studio Developer Command Prompt
    echo Or install Visual Studio Build Tools
)

REM 创建工具目录
if not exist "tools" mkdir tools
cd tools

REM 克隆vcpkg（如果不存在）
if not exist "vcpkg" (
    echo Cloning vcpkg...
    git clone https://github.com/Microsoft/vcpkg.git
    if %errorlevel% neq 0 (
        echo Error: Failed to clone vcpkg
        pause
        exit /b 1
    )
)

cd vcpkg

REM 构建vcpkg（如果不存在）
if not exist "vcpkg.exe" (
    echo Building vcpkg...
    call bootstrap-vcpkg.bat
    if %errorlevel% neq 0 (
        echo Error: Failed to build vcpkg
        pause
        exit /b 1
    )
)

REM 安装OpenCV
echo Installing OpenCV...
vcpkg install opencv:x64-windows
if %errorlevel% neq 0 (
    echo Error: Failed to install OpenCV
    echo Trying with different triplet...
    vcpkg install opencv:x86-windows
)

REM 集成到Visual Studio
echo Integrating vcpkg with Visual Studio...
vcpkg integrate install

echo.
echo OpenCV installation completed!
echo.
echo To use with CMake, add this to your CMakeLists.txt:
echo set(CMAKE_TOOLCHAIN_FILE "tools/vcpkg/scripts/buildsystems/vcpkg.cmake")
echo.
echo Or set environment variable:
echo set CMAKE_TOOLCHAIN_FILE=%CD%\scripts\buildsystems\vcpkg.cmake

cd ..\..
pause
